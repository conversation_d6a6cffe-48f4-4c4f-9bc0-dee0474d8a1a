[{"platform": "reddit", "post_id": "reddit_1<PERSON><PERSON><PERSON>h", "title": "Microsoft’s new AI “Copilot for Gaming” struggles to justify its existence | We're still a ways off from the conversational AI \"partner\" teased last year.", "content": "", "author": "ControlCAD", "created_time": "2025-03-14T03:17:55", "url": "https://reddit.com/r/microsoft/comments/1jaujoh/microsofts_new_ai_copilot_for_gaming_struggles_to/", "upvotes": 7, "comments_count": 1, "sentiment": "neutral", "engagement_score": 9.0, "source_subreddit": "microsoft", "hashtags": null, "ticker": "MSFT"}, {"platform": "reddit", "post_id": "reddit_1jauuzu", "title": "Next Gen Xbox console confirmed. A powerful, off the shelf, xbox branded gaming PC. 💯 Thoughts?", "content": "https://www.ign.com/articles/full-next-gen-xbox-reportedly-set-for-2027-xbox-branded-gaming-handheld-due-out-later-in-2025?fbclid=IwY2xjawJAgq1leHRuA2FlbQIxMQABHa8ka50M-PwDz5xy3oG3eQNfdvqmN_40H5RsXIizVQb1YjYi5wAWDZrXKg_aem_tCteoJjrHCLPzOv-DhrbE", "author": "Tripps0007-", "created_time": "2025-03-14T03:35:20", "url": "https://reddit.com/r/microsoft/comments/1jauuzu/next_gen_xbox_console_confirmed_a_powerful_off/", "upvotes": 0, "comments_count": 17, "sentiment": "neutral", "engagement_score": 34.0, "source_subreddit": "microsoft", "hashtags": null, "ticker": "MSFT"}, {"platform": "reddit", "post_id": "reddit_1jb5f4w", "title": "Epic Games is addressing one of Windows-on-Arm’s last big app compatibility gaps | Drivers and kernel-level software can't be translated automatically by Windows.", "content": "", "author": "ControlCAD", "created_time": "2025-03-14T14:38:38", "url": "https://reddit.com/r/microsoft/comments/1jb5f4w/epic_games_is_addressing_one_of_windowsonarms/", "upvotes": 107, "comments_count": 6, "sentiment": "neutral", "engagement_score": 119.0, "source_subreddit": "microsoft", "hashtags": null, "ticker": "MSFT"}, {"platform": "reddit", "post_id": "reddit_1jb8500", "title": "4 hours of interviews with one guy?", "content": "Hey everyone! \n\nI've been going through the recruiting process at Microsoft. I had a 1 hour technical interview and made it through that. I'm under the impression that that was the only technical portion of interviews. I know just got scheduled for my next round, and I have 4 hour long interviews on one day. Only one person is listed as the interviewer for all of them. \n\nShould I really expect to talk to just one person about the position for 4 hours in one day? I'm really not sure what all could be covered that needs that much time. The position is a low level (non senior, non management) technical role.\n\nThanks for any insight!", "author": "jay<PERSON>n", "created_time": "2025-03-14T16:35:00", "url": "https://reddit.com/r/microsoft/comments/1jb8500/4_hours_of_interviews_with_one_guy/", "upvotes": 5, "comments_count": 15, "sentiment": "bullish", "engagement_score": 35.0, "source_subreddit": "microsoft", "hashtags": null, "ticker": "MSFT"}]