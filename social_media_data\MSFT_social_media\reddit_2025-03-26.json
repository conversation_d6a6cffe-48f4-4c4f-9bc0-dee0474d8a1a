[{"platform": "reddit", "post_id": "reddit_1jk0mht", "title": "Microsoft Azure SWEs : Do you like work?", "content": "I'm wondering if the work is bearable/likeable as a full-time job, and if co-workers are hard-working but not toxic. Especially with the recent layoffs without severance.", "author": "No-Tangelo-1857", "created_time": "2025-03-26T01:45:39", "url": "https://reddit.com/r/microsoft/comments/1jk0mht/microsoft_azure_swes_do_you_like_work/", "upvotes": 5, "comments_count": 6, "sentiment": "bearish", "engagement_score": 17.0, "source_subreddit": "microsoft", "hashtags": null, "ticker": "MSFT"}, {"platform": "reddit", "post_id": "reddit_1jk91ej", "title": "Is Copilot+ PC Software Actually Limited to Having an \"NPU\" or Will It Eventually Be Compatible with Older PCs? How Good Is the Live Translation?", "content": "Historically Microsoft has launched proprietary software with \"recommended hardware\" and then \"minimum hardware\" specs. I'm wondering if this is perhaps the case with the Copilot+ feautures, which are described using similar terms: [https://www.microsoft.com/en-us/windows/copilot-plus-pcs?r=1#faq1](https://www.microsoft.com/en-us/windows/copilot-plus-pcs?r=1#faq1)\n\nIs an \"NPU\" actually a totally new component such that trying to run Copilot+ would be like trying to run a game without a videocard? ...or is Copilot+ just temporarily exclusive to hardware branded as \"Copilot+ ready\" to sell some new computers and it will eventually be available on PCs without those specs?\n\nI'm also wondering if anyone can speak to how effective the live translation is. I moved to Norway for a job and don't speak Norwegian yet, so I am often sitting in meetings without understanding a word of what is going on. Google translate and similar software have been basically useless for live translation. The Copilot+ live translation seems like the perfect solution to this if it works well.", "author": "<PERSON><PERSON><PERSON><PERSON>", "created_time": "2025-03-26T10:57:51", "url": "https://reddit.com/r/microsoft/comments/1jk91ej/is_copilot_pc_software_actually_limited_to_having/", "upvotes": 1, "comments_count": 2, "sentiment": "bearish", "engagement_score": 5.0, "source_subreddit": "microsoft", "hashtags": null, "ticker": "MSFT"}, {"platform": "reddit", "post_id": "reddit_1jkammt", "title": "Microsoft Defender for Endpoint Plan 1 vs Microsoft Defender for Endpoint Plan 2", "content": "Got about 400 users that need an endpoint protection plan...Wondering if it is worth paying the difference on Microsoft Defender for Endpoint Plan 1 and get Microsoft Defender for Endpoint Plan 2.... Getting hassled by auditors, I guess reports from sccm on the Microsoft defender that is shipped with windows doesn't cut it any more.\n\nWhat is the experience out here? Do you have an opinion on either of them, better yet, maybe both? I would like to hear it.", "author": "Kitchen-Magician-421", "created_time": "2025-03-26T12:31:26", "url": "https://reddit.com/r/microsoft/comments/1jkammt/microsoft_defender_for_endpoint_plan_1_vs/", "upvotes": 3, "comments_count": 3, "sentiment": "neutral", "engagement_score": 9.0, "source_subreddit": "microsoft", "hashtags": null, "ticker": "MSFT"}, {"platform": "reddit", "post_id": "reddit_1jkcn1d", "title": "Problems with Microsoft Learn?", "content": "Hi,\n\nMy Microsoft Learn webpages are not loading, no matter how many times i refresh it.  I'm learning C# and here's an example what a knowledge check page looks like for me: [https://imgur.com/a/3Cg5zIV](https://imgur.com/a/3Cg5zIV) \n\nCan anyone here help me?", "author": "villell", "created_time": "2025-03-26T14:08:56", "url": "https://reddit.com/r/microsoft/comments/1jkcn1d/problems_with_microsoft_learn/", "upvotes": 13, "comments_count": 17, "sentiment": "neutral", "engagement_score": 47.0, "source_subreddit": "microsoft", "hashtags": null, "ticker": "MSFT"}, {"platform": "reddit", "post_id": "reddit_1jkhgy9", "title": "Microsoft’s account sign-in UI gets a new design and dark mode", "content": "", "author": "BippityBoppityWhoops", "created_time": "2025-03-26T17:31:35", "url": "https://reddit.com/r/microsoft/comments/1jkhgy9/microsofts_account_signin_ui_gets_a_new_design/", "upvotes": 57, "comments_count": 4, "sentiment": "neutral", "engagement_score": 65.0, "source_subreddit": "microsoft", "hashtags": null, "ticker": "MSFT"}, {"platform": "reddit", "post_id": "reddit_1jkhv9d", "title": "Around six weeks ago, I applied to a couple software development jobs at Microsoft, including the \"Neurodiversity Hiring Program\". One application is in review, one is transferred. How long should I keep waiting for?", "content": "Any suggestions for following up?", "author": "Cheetah3051", "created_time": "2025-03-26T17:47:32", "url": "https://reddit.com/r/microsoft/comments/1jkhv9d/around_six_weeks_ago_i_applied_to_a_couple/", "upvotes": 0, "comments_count": 3, "sentiment": "bullish", "engagement_score": 6.0, "source_subreddit": "microsoft", "hashtags": null, "ticker": "MSFT"}, {"platform": "reddit", "post_id": "reddit_1jkj5xm", "title": "Microsoft pulls back from more data center leases in US and Europe, analysts say", "content": "", "author": "ControlCAD", "created_time": "2025-03-26T18:40:41", "url": "https://reddit.com/r/microsoft/comments/1jkj5xm/microsoft_pulls_back_from_more_data_center_leases/", "upvotes": 53, "comments_count": 7, "sentiment": "neutral", "engagement_score": 67.0, "source_subreddit": "microsoft", "hashtags": null, "ticker": "MSFT"}]