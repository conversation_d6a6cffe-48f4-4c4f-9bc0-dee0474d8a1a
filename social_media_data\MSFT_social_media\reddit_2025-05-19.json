[{"platform": "reddit", "post_id": "reddit_1kq3yy5", "title": "How did the recent layoff affect India?", "content": "So Microsoft laid off 6000 people last week! I am curious to know what the India headcount is and how many orgs/teams in India got affected and how does this compare to the rest of the world?\n\n", "author": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "created_time": "2025-05-19T05:26:38", "url": "https://reddit.com/r/microsoft/comments/1kq3yy5/how_did_the_recent_layoff_affect_india/", "upvotes": 22, "comments_count": 23, "sentiment": "neutral", "engagement_score": 68.0, "source_subreddit": "microsoft", "hashtags": null, "ticker": "MSFT"}, {"platform": "reddit", "post_id": "reddit_1kq52si", "title": "Hello all!", "content": "\nI am currently evaluating an offer from\nMicrosoft edge distribution team based out of Hyderabad. Anyone here who can give more insight on this team? ", "author": "9f8e0chp", "created_time": "2025-05-19T06:41:00", "url": "https://reddit.com/r/microsoft/comments/1kq52si/hello_all/", "upvotes": 3, "comments_count": 2, "sentiment": "neutral", "engagement_score": 7.0, "source_subreddit": "microsoft", "hashtags": null, "ticker": "MSFT"}, {"platform": "reddit", "post_id": "reddit_1kq95rb", "title": "Microsoft wants AI 'agents' to work together and remember things", "content": "", "author": "adriano26", "created_time": "2025-05-19T11:20:13", "url": "https://reddit.com/r/microsoft/comments/1kq95rb/microsoft_wants_ai_agents_to_work_together_and/", "upvotes": 11, "comments_count": 3, "sentiment": "neutral", "engagement_score": 17.0, "source_subreddit": "microsoft", "hashtags": null, "ticker": "MSFT"}, {"platform": "reddit", "post_id": "reddit_1kq9tu2", "title": "The structure and the 90% success rate that follows", "content": "$TSLA full move from the April coil to the May breakout and last week’s pause in 60 seconds.\n\nhttps://reddit.com/link/1kq9tu2/video/uritm86y8q1f1/player\n\nSelling verticals \\*above the pause\\* after a clean breakout wins \\~90% of the time.  \n\nhttps://preview.redd.it/hk5ucl2g9q1f1.png?width=1785&format=png&auto=webp&s=56e3f8316eadde59788b5e15c93e7f23c015920d\n\nBack-tested across 1,000+ setups in $SPX, $MSFT, $TSLA.  Direction of the break from the coil doesn't matter.\n\nNot prediction. Not guessing.  \n\nJust understanding when the fire’s out and edge returns. 🐀", "author": "GIANTKI113R", "created_time": "2025-05-19T11:58:01", "url": "https://reddit.com/r/options/comments/1kq9tu2/the_structure_and_the_90_success_rate_that_follows/", "upvotes": 251, "comments_count": 37, "sentiment": "bullish", "engagement_score": 325.0, "source_subreddit": "options", "hashtags": null, "ticker": "MSFT"}, {"platform": "reddit", "post_id": "reddit_1kqfm24", "title": "Disruptions at Microsoft build", "content": "Hey folks, am watching Build online & hearing people shouting during <PERSON><PERSON><PERSON>'s keynote.\n\nAnyone there that can comment on what the disruption was about? \n\nNot sure what people hope to achieve with this activity, but not sure it engenders the sympathy they are hoping for....", "author": "Haggis_the_dog", "created_time": "2025-05-19T16:10:04", "url": "https://reddit.com/r/microsoft/comments/1kqfm24/disruptions_at_microsoft_build/", "upvotes": 155, "comments_count": 95, "sentiment": "neutral", "engagement_score": 345.0, "source_subreddit": "microsoft", "hashtags": null, "ticker": "MSFT"}, {"platform": "reddit", "post_id": "reddit_1kqg8ph", "title": "Microsoft to offer rival AI models from own datacenter; launches AI coding agent", "content": "", "author": "newyork99", "created_time": "2025-05-19T16:34:44", "url": "https://reddit.com/r/microsoft/comments/1kqg8ph/microsoft_to_offer_rival_ai_models_from_own/", "upvotes": 22, "comments_count": 1, "sentiment": "neutral", "engagement_score": 24.0, "source_subreddit": "microsoft", "hashtags": null, "ticker": "MSFT"}, {"platform": "reddit", "post_id": "reddit_1kqh8u8", "title": "It’ll soon be free to publish apps to the Microsoft Store", "content": "", "author": "ScootSchloingo", "created_time": "2025-05-19T17:13:50", "url": "https://reddit.com/r/microsoft/comments/1kqh8u8/itll_soon_be_free_to_publish_apps_to_the/", "upvotes": 52, "comments_count": 9, "sentiment": "neutral", "engagement_score": 70.0, "source_subreddit": "microsoft", "hashtags": null, "ticker": "MSFT"}, {"platform": "reddit", "post_id": "reddit_1kqhwrq", "title": "Microsoft platforming Elon Musk at Build 2025", "content": "Please help me understand why <PERSON> thinks it would be a good idea to platform Elon at Build. He has shown his political views with much furvor at CPAC, shown his disregard of a functioning democracy and directly poses a legitimate national security risk with his ham-fisted intervention in our national agencies. They could have announced the xAI models and moved on, but instead they show a 6 minute interview between him and <PERSON><PERSON><PERSON>. This is not the kind of association they would want to espouse when they try to appeal to European businesses and build trust with governments as they have tried with their recent statements about continued and stable relations in the region.", "author": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "created_time": "2025-05-19T17:39:35", "url": "https://reddit.com/r/microsoft/comments/1kqhwrq/microsoft_platforming_elon_musk_at_build_2025/", "upvotes": 890, "comments_count": 133, "sentiment": "neutral", "engagement_score": 1156.0, "source_subreddit": "microsoft", "hashtags": null, "ticker": "MSFT"}, {"platform": "reddit", "post_id": "reddit_1kqjd5b", "title": "Anything worth getting excited about from Microsoft Build 2025?", "content": "I'm too lazy to watch Build 2025 and some websites summarizing were either using fluffy, abstract, corporate speak type words like \"AI-powered internet\" and discussing capabilities that are always brought up but seems to be more hype and less results (like AI-cancer research and real-time spoken language translation).\n\n  \nDid anyone actually watch Build 2025 and see something that was exciting to them?", "author": "Upset-Ad-8704", "created_time": "2025-05-19T18:35:33", "url": "https://reddit.com/r/microsoft/comments/1kqjd5b/anything_worth_getting_excited_about_from/", "upvotes": 59, "comments_count": 27, "sentiment": "neutral", "engagement_score": 113.0, "source_subreddit": "microsoft", "hashtags": null, "ticker": "MSFT"}, {"platform": "reddit", "post_id": "reddit_1kqjw5c", "title": "Microsoft Edit is now open source | Windows CLI text editor", "content": "Windows command-line text editor written in Rust", "author": "thegravity98ms2", "created_time": "2025-05-19T18:55:58", "url": "https://reddit.com/r/microsoft/comments/1kqjw5c/microsoft_edit_is_now_open_source_windows_cli/", "upvotes": 11, "comments_count": 2, "sentiment": "neutral", "engagement_score": 15.0, "source_subreddit": "microsoft", "hashtags": null, "ticker": "MSFT"}, {"platform": "reddit", "post_id": "reddit_1kqk32i", "title": "Microsoft Employee whistleblower disrupts CEO’s keynote saying Microsoft is complicit in Israel war crimes.", "content": "The protester sent an email to the whole company. I linked the email.\n\n\"I went into work everyday plagued by thoughts of the suffering that is being inflicted by a United States-Israeli war machine that runs on Azure.\"\n\n\"Microsoft openly admitted to allowing the Israel Ministry of Defense “special access to our technologies beyond the terms of our commercial agreements”\".", "author": "xXxwiskersxXx", "created_time": "2025-05-19T19:03:03", "url": "https://reddit.com/r/microsoft/comments/1kqk32i/microsoft_employee_whistleblower_disrupts_ceos/", "upvotes": 272, "comments_count": 65, "sentiment": "neutral", "engagement_score": 402.0, "source_subreddit": "microsoft", "hashtags": null, "ticker": "MSFT"}, {"platform": "reddit", "post_id": "reddit_1kqlpcg", "title": "Microsoft closes 9-year-old feature request, open-sources Windows Subsystem for Linux", "content": "", "author": "ControlCAD", "created_time": "2025-05-19T20:06:23", "url": "https://reddit.com/r/microsoft/comments/1kqlpcg/microsoft_closes_9yearold_feature_request/", "upvotes": 216, "comments_count": 2, "sentiment": "neutral", "engagement_score": 220.0, "source_subreddit": "microsoft", "hashtags": null, "ticker": "MSFT"}]