[{"platform": "reddit", "post_id": "reddit_1k4jmig", "title": "Windows Phone", "content": "Hi everyone,\n\nOver the past few days, I discovered this group after buying a Lumia 1020. I’ve always loved Windows Phone, but I thought it was completely dead, just as Microsoft had declared. However, I now see that there’s a surprisingly large group of people who are still trying to keep this operating system alive. In one post, for example, a user created polls to estimate the timeline of Windows Phone’s downfall and people were genuinely excited, simply because it meant someone was doing something.\n\nI believe this is just the tip of the iceberg. I would love to hear your opinions about reviving the Windows Phone. Would you still buy it if certain improvements are made? If yes, what kind of improvements would you like to see?", "author": "Outside-Round428", "created_time": "2025-04-21T17:44:43", "url": "https://reddit.com/r/microsoft/comments/1k4jmig/windows_phone/", "upvotes": 6, "comments_count": 10, "sentiment": "bullish", "engagement_score": 26.0, "source_subreddit": "microsoft", "hashtags": null, "ticker": "MSFT"}, {"platform": "reddit", "post_id": "reddit_1k4k18u", "title": "Microsoft’s “1‑bit” AI model runs on a CPU only, while matching larger systems", "content": "", "author": "BippityBoppityWhoops", "created_time": "2025-04-21T18:00:34", "url": "https://reddit.com/r/microsoft/comments/1k4k18u/microsofts_1bit_ai_model_runs_on_a_cpu_only_while/", "upvotes": 35, "comments_count": 2, "sentiment": "neutral", "engagement_score": 39.0, "source_subreddit": "microsoft", "hashtags": null, "ticker": "MSFT"}]