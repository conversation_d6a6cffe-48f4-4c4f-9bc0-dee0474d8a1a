[{"platform": "reddit", "post_id": "reddit_1jvraf8", "title": "Microsoft purview SWEs - Do you like your work?", "content": "Those working on purview, how is your work like and do you enjoy it? \nDo you have hardworking and non-toxic, helpful colleagues? \nDo you think you have enough room to talk and present your ideas? And get enough time to complete your work without much pressure?", "author": "moon_and_light", "created_time": "2025-04-10T06:25:18", "url": "https://reddit.com/r/microsoft/comments/1jvraf8/microsoft_purview_swes_do_you_like_your_work/", "upvotes": 1, "comments_count": 1, "sentiment": "neutral", "engagement_score": 3.0, "source_subreddit": "microsoft", "hashtags": null, "ticker": "MSFT"}, {"platform": "reddit", "post_id": "reddit_1jvsfmj", "title": "Apple Falls By 5% Due to Tariffs and Microsoft Becomes the Most Valuable Company in the World", "content": "", "author": "Fabulous_Bluebird931", "created_time": "2025-04-10T07:48:50", "url": "https://reddit.com/r/microsoft/comments/1jvsfmj/apple_falls_by_5_due_to_tariffs_and_microsoft/", "upvotes": 159, "comments_count": 8, "sentiment": "bearish", "engagement_score": 175.0, "source_subreddit": "microsoft", "hashtags": null, "ticker": "MSFT"}, {"platform": "reddit", "post_id": "reddit_1jvyx2r", "title": "Office 365 Family Subscription Issue - down as per Microsoft tracker", "content": "It's not just you. It's down globally and is on Microsoft issue tracker\n\nWasted an hour troubleshooting before finding out. Tracker :\n\nhttps://portal.office.com/servicestatus", "author": "xcal15", "created_time": "2025-04-10T14:13:05", "url": "https://reddit.com/r/microsoft/comments/1jvyx2r/office_365_family_subscription_issue_down_as_per/", "upvotes": 4, "comments_count": 3, "sentiment": "neutral", "engagement_score": 10.0, "source_subreddit": "microsoft", "hashtags": null, "ticker": "MSFT"}, {"platform": "reddit", "post_id": "reddit_1jvyxnm", "title": "Microsoft Security Update Guide Error? Windows 2019 Build Number for 2025-04 CU", "content": "Folks:\n\nHas anyone else noticed that the Build Number is showing as ending as 7137 for this month's Server 2019 CU, but if you go to the actual KB article (5055519) and if you open CMD on an updated Server 2019 instance, the build number ends in 7136?\n\nOops... apparently MSRC is more subjected to fat-fingering than one might expect vs. automation.  :P\n\nAlso, I'm not sure what the best way to report this to Microsoft is, and who knows, we're almost 48 hours after the fact, so surely they've already been informed?\n\n  \nEDIT 1:  I also just discovered than Windows 11 23H2 is also incorrectly numbered in the MSUG, ending in 5191 for a build number while the KB article and actual systems report 5189.", "author": "DeltaSierra426", "created_time": "2025-04-10T14:13:47", "url": "https://reddit.com/r/microsoft/comments/1jvyxnm/microsoft_security_update_guide_error_windows/", "upvotes": 2, "comments_count": 3, "sentiment": "neutral", "engagement_score": 8.0, "source_subreddit": "microsoft", "hashtags": null, "ticker": "MSFT"}, {"platform": "reddit", "post_id": "reddit_1jw37rh", "title": "Does anyone know what this subdomain is?", "content": "Was going through the A records and I found [minervavaultstg.microsoft.com](http://minervavaultstg.microsoft.com)\n\nI asked Microsoft support and they said it's likely an internal thing. Was wondering if anyone here had any extra details I can learn about this subdomain since it refuses to load on my browser and I can't even ping it.", "author": "Select_Pay_1564", "created_time": "2025-04-10T17:14:41", "url": "https://reddit.com/r/microsoft/comments/1jw37rh/does_anyone_know_what_this_subdomain_is/", "upvotes": 0, "comments_count": 5, "sentiment": "neutral", "engagement_score": 10.0, "source_subreddit": "microsoft", "hashtags": null, "ticker": "MSFT"}, {"platform": "reddit", "post_id": "reddit_1jw75vg", "title": "CoPilot Studio for Microsoft 365", "content": "Hello all,\n\nI'm looking into CoPilot Studio for Microsoft 365 and I just had one question that I can't seem to find in the Microsoft forums, or at least the information is relatively difficult to find.  If I want to be the main person creating agents for my organization, and I want to publish those agents for use as internal tools and resources, would I need only one CoPilot Studio for Microsoft 365 license or would my entire organization need it?  There only needs to be one or two people who actually have control over the agents, so ideally, only one or two licenses would be necessary and then the agents can be published for the remaining users to utilize. ", "author": "BobIsMyCableGuy", "created_time": "2025-04-10T19:57:41", "url": "https://reddit.com/r/microsoft/comments/1jw75vg/copilot_studio_for_microsoft_365/", "upvotes": 0, "comments_count": 2, "sentiment": "neutral", "engagement_score": 4.0, "source_subreddit": "microsoft", "hashtags": null, "ticker": "MSFT"}]