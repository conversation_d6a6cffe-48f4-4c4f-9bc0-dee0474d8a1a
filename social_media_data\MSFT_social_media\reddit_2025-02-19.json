[{"platform": "reddit", "post_id": "reddit_1iszuff", "title": "Does anyone else fondly look back at the Windows Insider Program from 2014-15?", "content": "2014-15 was when the Windows Insider Program got beta builds of Windows 10. I enrolled on Day 1, I would always download the updates ASAP on the family computer no matter how much it pissed off my sister. I got excited for new builds on Wednesdays and often looked at  the Windows blog as well as winbeta.org before its rebrands. I think it played a big role in my interest in software and tech.", "author": "TheTwelveYearOld", "created_time": "2025-02-19T07:24:39", "url": "https://reddit.com/r/microsoft/comments/1iszuff/does_anyone_else_fondly_look_back_at_the_windows/", "upvotes": 11, "comments_count": 4, "sentiment": "neutral", "engagement_score": 19.0, "source_subreddit": "microsoft", "hashtags": null, "ticker": "MSFT"}, {"platform": "reddit", "post_id": "reddit_1it2cnb", "title": "Microsoft Solutions Partner", "content": "Our company specializes in IT solutions, and providing Infrastructure as a Service (IaaS) and Software as a Service (SaaS). Unlike traditional service providers, we don’t have external customers in a direct sales model; instead, we deliver services on behalf of our customers, functioning as a third-party service provider. We managed our current customers' azure services under our own tenant.\n\nCurrently, we are a legacy Gold member, but Microsoft discontinued renewals as of January 22. We still have access until November 6, 2025, and we are planning to enroll in the Solutions Partner program for either Modern Work (Enterprise) or Infrastructure.\n\nAccording to Microsoft Partner Center, achieving a Solutions Partner designation is measured by performance, skilling, and customer success.\n\nOur situation is that even with significant effort, we could acquire at most five customers. Beyond that, we would not be able to expand our customer base for the foreseeable future.\n\nSince we cannot continuously acquire new customers, meeting the customer success requirements seems impossible. What we would like to understand is: If we don’t bring in new customers, can we still earn customer success points through deployments and usage growth of existing customers for adding new VMs and Azure subscriptions? Would these count toward the performance metrics? Please advise how do we manage to become Microsoft Partner.", "author": "sleepeezz", "created_time": "2025-02-19T10:24:20", "url": "https://reddit.com/r/microsoft/comments/1it2cnb/microsoft_solutions_partner/", "upvotes": 0, "comments_count": 0, "sentiment": "bullish", "engagement_score": 0.0, "source_subreddit": "microsoft", "hashtags": null, "ticker": "MSFT"}, {"platform": "reddit", "post_id": "reddit_1it2pb6", "title": "Azure AI services for contract analysis", "content": "Hi everyone🖐\n\nI would like to know if an AI agent or automation flow can be created in Azure using Azure AI services, OpenAI services, or any other Azure services to help me with the following:\n\nI have a database—a folder in SharePoint—where I store general terms and conditions of sales, template sales agreements, main contractual provisions, and similar documents.\n\nWhenever I receive agreements or contracts from potential clients, I want them to be automatically compared against the database. The AI should answer my predefined questions, cite the relevant page and paragraph, and generate a report.\n\nHere are some of the questions:\n\n1. Do the provisions on warranty and liability in \\[Agreement A\\] and \\[Agreement B\\] Standard Terms and Conditions deviate from the warranty and liability provisions we typically include in our agreements? What kind of risks result from these deviations?\n2. Do the provisions in the provided agreements deviate from those we usually include in our agreements in any other way that poses a substantial risk to \\[Company X\\]?\n3. Are there any contractual penalties included in \\[Agreement A\\] and \\[Agreement B\\] Standard Terms and Conditions provided by \\[Supplier Y\\]?\n\nI want all of this to be done autonomously using an AI agent.\n\nDoes anyone have any ideas on how this can be achieved in Azure? Also can my logic be improved?", "author": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "created_time": "2025-02-19T10:48:13", "url": "https://reddit.com/r/microsoft/comments/1it2pb6/azure_ai_services_for_contract_analysis/", "upvotes": 0, "comments_count": 1, "sentiment": "neutral", "engagement_score": 2.0, "source_subreddit": "microsoft", "hashtags": null, "ticker": "MSFT"}, {"platform": "reddit", "post_id": "reddit_1it3jdt", "title": "Microsoft is spending $700 million to ramp up security and computing power in Poland | AI infrastructure and cybersecurity are getting a boost from Microsoft", "content": "", "author": "ControlCAD", "created_time": "2025-02-19T11:42:13", "url": "https://reddit.com/r/microsoft/comments/1it3jdt/microsoft_is_spending_700_million_to_ramp_up/", "upvotes": 76, "comments_count": 2, "sentiment": "neutral", "engagement_score": 80.0, "source_subreddit": "microsoft", "hashtags": null, "ticker": "MSFT"}, {"platform": "reddit", "post_id": "reddit_1it9xrk", "title": "Microsoft demonstrates working qubits based on exotic physics | Stronger evidence for a hypothetical quasiparticle, plus actual processing hardware.", "content": "", "author": "ControlCAD", "created_time": "2025-02-19T16:42:20", "url": "https://reddit.com/r/microsoft/comments/1it9xrk/microsoft_demonstrates_working_qubits_based_on/", "upvotes": 174, "comments_count": 9, "sentiment": "bullish", "engagement_score": 192.0, "source_subreddit": "microsoft", "hashtags": null, "ticker": "MSFT"}, {"platform": "reddit", "post_id": "reddit_1itgmm2", "title": "10-core vs 12-core", "content": "i’m hoping to get opinions ahead of purchasing a new laptop. \ni love the microsoft surface laptop and have used it both personally and professionally in the past. i’m getting a new one for myself at my office which is the Surface Laptop, Copilot+ PC. there is the option for the Snapdragon X Plus (10 Core) or the Snapdragon X Elite (12 core) processor - with about a $250 difference between the two. \ni handle firm business operations and am the executive assistant to our CEO. i love to multitask and am often using multiple programs and have numerous chrome tabs open, but I am not doing things like video editing. i use outlook, dropbox, adobe pro, chrome (for QBO, bank management, general research) most extensively - i also use word, excel, one note and a scanning program on occasion. \n\nshould i be ok if i get the 10 core? or is the additional $250 really worth it for the 12 core?\n\nthanks so much! ", "author": "heatles22", "created_time": "2025-02-19T21:07:02", "url": "https://reddit.com/r/microsoft/comments/1itgmm2/10core_vs_12core/", "upvotes": 0, "comments_count": 3, "sentiment": "bearish", "engagement_score": 6.0, "source_subreddit": "microsoft", "hashtags": null, "ticker": "MSFT"}, {"platform": "reddit", "post_id": "reddit_1itgoc4", "title": "Microsoft releases Muse, an AI model for 3D game environment creation", "content": "", "author": "dreadpiratewombat", "created_time": "2025-02-19T21:08:56", "url": "https://reddit.com/r/microsoft/comments/1itgoc4/microsoft_releases_muse_an_ai_model_for_3d_game/", "upvotes": 20, "comments_count": 2, "sentiment": "neutral", "engagement_score": 24.0, "source_subreddit": "microsoft", "hashtags": null, "ticker": "MSFT"}]