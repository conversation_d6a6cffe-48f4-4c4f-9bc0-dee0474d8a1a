[{"platform": "reddit", "post_id": "reddit_1icl0xg", "title": "Is there like a Lite Version of Office?", "content": "Like i just download LibreOffice and the whole package is 300MB\n\nCompared to my Licensed Office ProPlus LTSC 2019 which is +4GB\n\nLike i litterally use ot like 3 times a month to edit a word document or make a simple presentation.\n\n\n\nEdit: if the post is not clear\n\nI am asking sbout a specific version of office or modified office, not an alternative to office suite\n\nLike is there a version called Office 2021 Basic that has like only word, powerpoint, excel, and access for under 1GB in file size, removing other functionality like OneDrive and onenote integration and offloading the smartArt database to cloud(as office 365)", "author": "Bebo991_Gaming", "created_time": "2025-01-29T03:49:53", "url": "https://reddit.com/r/microsoft/comments/1icl0xg/is_there_like_a_lite_version_of_office/", "upvotes": 1, "comments_count": 14, "sentiment": "bullish", "engagement_score": 29.0, "source_subreddit": "microsoft", "hashtags": null, "ticker": "MSFT"}, {"platform": "reddit", "post_id": "reddit_1icla6d", "title": "Microsoft CEO <PERSON><PERSON><PERSON> touts DeepSeek's open-source AI as \"super impressive\": \"We should take the developments out of China very, very seriously\"", "content": "Microsoft's CEO says AI developments from China should be taken very seriously amid the DeepSeek AI frenzy.", "author": "ControlCAD", "created_time": "2025-01-29T04:03:57", "url": "https://reddit.com/r/microsoft/comments/1icla6d/microsoft_ceo_satya_nadella_touts_deepseeks/", "upvotes": 693, "comments_count": 41, "sentiment": "neutral", "engagement_score": 775.0, "source_subreddit": "microsoft", "hashtags": null, "ticker": "MSFT"}, {"platform": "reddit", "post_id": "reddit_1icukn5", "title": "Disconnecting Microsoft Services", "content": "Hey guys, I work a lot with Microsoft Software for my Job. Outlook, OneDrive, 365 and so on and my girlfriend does too. My Computer broke some days ago and so I logged into my Microsoft Accounts on her Laptop and now since my Computer Works again I wanted to disconnect all Thors Services of mine from her Laptop. I logged off all Services but she still has all my data on her one Drive and also gets all my contacts as recommendations on her 365 which annoys her a lot. Any idea how to fully disconnect again?", "author": "_Naydra_", "created_time": "2025-01-29T14:09:20", "url": "https://reddit.com/r/microsoft/comments/1icukn5/disconnecting_microsoft_services/", "upvotes": 2, "comments_count": 1, "sentiment": "neutral", "engagement_score": 4.0, "source_subreddit": "microsoft", "hashtags": null, "ticker": "MSFT"}, {"platform": "reddit", "post_id": "reddit_1icunui", "title": "Is the Segoe UI (Variable) fonts free to use?", "content": "Hey everyone\n\nI’ve been wondering if the fonts have a license or rights similar to Roboto- where they can be used without extra stairs? I’ve been using the font at my organization for the past 4 years.\n\nI <PERSON><PERSON> thought Microsoft kinda allows that unlike Apple.", "author": "new-romantics89", "created_time": "2025-01-29T14:13:30", "url": "https://reddit.com/r/microsoft/comments/1icunui/is_the_segoe_ui_variable_fonts_free_to_use/", "upvotes": 0, "comments_count": 8, "sentiment": "neutral", "engagement_score": 16.0, "source_subreddit": "microsoft", "hashtags": null, "ticker": "MSFT"}, {"platform": "reddit", "post_id": "reddit_1iczvii", "title": "iPhone and Android integration can be done with the start menu of Windows 11.", "content": "", "author": "Novel_Negotiation224", "created_time": "2025-01-29T17:52:50", "url": "https://reddit.com/r/microsoft/comments/1iczvii/iphone_and_android_integration_can_be_done_with/", "upvotes": 4, "comments_count": 0, "sentiment": "neutral", "engagement_score": 4.0, "source_subreddit": "microsoft", "hashtags": null, "ticker": "MSFT"}, {"platform": "reddit", "post_id": "reddit_1id14ac", "title": "Microsoft and OpenAI investigate whether DeepSeek illicitly obtained data from ChatGPT", "content": "", "author": "ControlCAD", "created_time": "2025-01-29T18:42:36", "url": "https://reddit.com/r/microsoft/comments/1id14ac/microsoft_and_openai_investigate_whether_deepseek/", "upvotes": 90, "comments_count": 45, "sentiment": "neutral", "engagement_score": 180.0, "source_subreddit": "microsoft", "hashtags": null, "ticker": "MSFT"}, {"platform": "reddit", "post_id": "reddit_1id3ctn", "title": "Imagine having unparalleled brand capital with the sinplest of words \"Office\" and ditching it entirely for the catchy \"Microsoft 365 copilot\"", "content": "Honestly, what are Microsoft doing?", "author": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "created_time": "2025-01-29T20:13:03", "url": "https://reddit.com/r/microsoft/comments/1id3ctn/imagine_having_unparalleled_brand_capital_with/", "upvotes": 193, "comments_count": 43, "sentiment": "neutral", "engagement_score": 279.0, "source_subreddit": "microsoft", "hashtags": null, "ticker": "MSFT"}, {"platform": "reddit", "post_id": "reddit_1id4hz9", "title": "Microsoft Tops Global Game Sales Charts in December 2024", "content": "", "author": "johanas25", "created_time": "2025-01-29T21:01:06", "url": "https://reddit.com/r/microsoft/comments/1id4hz9/microsoft_tops_global_game_sales_charts_in/", "upvotes": 8, "comments_count": 0, "sentiment": "neutral", "engagement_score": 8.0, "source_subreddit": "microsoft", "hashtags": null, "ticker": "MSFT"}, {"platform": "reddit", "post_id": "reddit_1id5l09", "title": "The Current State of Software Engineering Interview is Deeply Flawed", "content": "Someone posted this on LinkedIn:\n\n*The current state of software engineering interviews is deeply flawed. A friend of mine is considering leaving their job just to focus on studying full-time for interviews. Think about that—interviews have become so demanding and disconnected from day-to-day work that candidates feel the need to dedicate months solely to preparation.*\n\n*This isn’t just about solving complex algorithms or mastering system design; it’s about creating a process that values practical skills, creativity, and the ability to collaborate—qualities that truly define great engineers.*\n\n*We need to ask ourselves: are we testing for the right things? Or are we unintentionally gatekeeping talent by prioritizing who can memorize LeetCode problems over who can build scalable, impactful software?*\n\n[Post | Feed | LinkedIn](https://www.linkedin.com/feed/update/urn:li:activity:7288556194070200321/)\n\n  \nHaving interviewed for a SWE role and worked for other big non-tech companies. I would say the interview is deeply flawed at Microsoft. I've never seen a place that is more focused on algorithm and design pattern knowledge. Solving LeetCode problems, You can be passionate about the work, hard-working, eager to learn and growth, have a breath of knowledge, creative, able to collaborate and work with others but if you can't code a link list in C# (which is something rarely done or used) then no hire. I would like to see the SWE in Test roles brought back but it may be too late. ", "author": "Zestyclose_Depth_196", "created_time": "2025-01-29T21:45:51", "url": "https://reddit.com/r/microsoft/comments/1id5l09/the_current_state_of_software_engineering/", "upvotes": 10, "comments_count": 5, "sentiment": "bullish", "engagement_score": 20.0, "source_subreddit": "microsoft", "hashtags": null, "ticker": "MSFT"}]