[{"platform": "reddit", "post_id": "reddit_1igjk2g", "title": "Windows on ARM is actually good!", "content": "Hey, \nI recently got a new Copilot+ PC because it was the only good laptop in the price range (Go figure). It has the snapdragon X plus 42-100 in it, 32GB of RAM and 1TB of storage. I got it for $1248 AUD. Anyway, multiple apps I went to install said no arm compatibility and it won't work. I click install and it works flawlessly! Honestly, I was using Linux before getting my laptop and now this is pulling me back to Windows! The battery life is awesome and the fans haven't kicked in once.\nThank you Microsoft and thank you Len<PERSON>!\n\nThe only issue is <PERSON><PERSON><PERSON> doesn't work :(", "author": "deleted", "created_time": "2025-02-03T07:15:18", "url": "https://reddit.com/r/microsoft/comments/1igjk2g/windows_on_arm_is_actually_good/", "upvotes": 98, "comments_count": 42, "sentiment": "neutral", "engagement_score": 182.0, "source_subreddit": "microsoft", "hashtags": null, "ticker": "MSFT"}, {"platform": "reddit", "post_id": "reddit_1ign8ou", "title": "Starting with Engage -  what are your best tips ?", "content": "Hi redittors, just like many other companies, we're switching from Facebook Workplace to Microsoft Engage. I'm rather happy about it because we'll have a single tool for everything.   \nI had specific questions and would also like to have your opinion and best tips about using Engage ?\n\n\\- image size to post on communities: are there ideal size to use? I could'nt find information online yet\n\n\\- discussion VS praise: how do you use praise? how do your communities react to them?\n\nThanks, i'll be happy to discuss it with you !", "author": "helag<PERSON>", "created_time": "2025-02-03T11:45:15", "url": "https://reddit.com/r/microsoft/comments/1ign8ou/starting_with_engage_what_are_your_best_tips/", "upvotes": 1, "comments_count": 0, "sentiment": "neutral", "engagement_score": 1.0, "source_subreddit": "microsoft", "hashtags": null, "ticker": "MSFT"}, {"platform": "reddit", "post_id": "reddit_1igughm", "title": "Question about Company Store", "content": "My big brother works for MS, so I'm able to access the company store via the Friends ane Family system, right?\nI was wondering if the company store has a \"funds\" system similar to that of Steam, where you can add money into your account to get games with instead of directly using your credit card. Is this system also in the normal MS store included in Windows?", "author": "Puzzleheaded_Hat2452", "created_time": "2025-02-03T17:24:36", "url": "https://reddit.com/r/microsoft/comments/1igughm/question_about_company_store/", "upvotes": 0, "comments_count": 6, "sentiment": "neutral", "engagement_score": 12.0, "source_subreddit": "microsoft", "hashtags": null, "ticker": "MSFT"}, {"platform": "reddit", "post_id": "reddit_1igustt", "title": "If I Had Only Knew.. Before Joining MSFT", "content": "Let me first say Microsoft is a great company. But before accepting the offer there are some things I wish I did a better job of before saying I accept.\n\n* Researched the role a lot more on sites like Reddit and Glassdoor - Titles can be misleading.\n* Knew the level I was coming in at - Not just the number and salary but where you are on the ladder and compare it to where you are now. Be comfortable if you do decide to jump because it might be a while before you move up. Just to note, there are \"firewalls\" between certain levels like 62-63. The criteria to get through the firewalls are not just your average \"I do a good job so promote me.\" So you might want to fight that battle before joining to make sure you come in where you should be based on your experience. A lot of people come in under where they were previously. I've seen VPs come in at Sr. level. Some people just say if the pay is equivalent or better then I'm good, like I did. Microsoft pays more than average and I wouldn't do that again.\n* Knew that what org you come into matters - Don't just think because you are in the building it's easy to change rooms. There are internal biases between rooms and even teams.\n* (As you probably can tell by the recent layoffs) Knew performance matters - I knew this. Duh. Like everywhere performance matters. BUT to others, if you are not a self motivated individual then don't waste your time. Just to note, it's stressful working here. So if you don't do stress well then stay where you are at.\n\nAgain, great company, benefits, learning opportunities and you work with tons of smart people. I'm good.", "author": "Zestyclose_Depth_196", "created_time": "2025-02-03T17:38:12", "url": "https://reddit.com/r/microsoft/comments/1igustt/if_i_had_only_knew_before_joining_msft/", "upvotes": 248, "comments_count": 47, "sentiment": "neutral", "engagement_score": 342.0, "source_subreddit": "microsoft", "hashtags": null, "ticker": "MSFT"}]