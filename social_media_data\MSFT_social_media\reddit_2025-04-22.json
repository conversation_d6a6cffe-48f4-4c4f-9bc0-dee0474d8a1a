[{"platform": "reddit", "post_id": "reddit_1k4zvh5", "title": "$AMZN has delayed some commitments around new data center leases, Wells Fargo analysts said Monday, the latest sign that economic concerns may be affecting tech companies’ spending plans.", "content": "A week ago, a Microsoft executive said the software company was slowing down or temporarily holding off on advancing early build-outs. Amazon Web Services (AWS) and Microsoft are the leading providers of cloud infrastructure, and both have ramped up their capital expenditures in recent quarters to meet the demands of the generative artificial intelligence boom.  \n  \n“Over the weekend, we heard from several industry sources that AWS has paused a portion of its leasing discussions on the colocation side (particularly international ones),” Wells Fargo analysts wrote in a note. They added that “the positioning is similar to what we’ve heard recently from MSFT,” in that both companies are reeling in some new projects but not canceling signed", "author": "Flat_Nose_8811", "created_time": "2025-04-22T06:44:52", "url": "https://reddit.com/r/investing_discussion/comments/1k4zvh5/amzn_has_delayed_some_commitments_around_new_data/", "upvotes": 3, "comments_count": 1, "sentiment": "neutral", "engagement_score": 5.0, "source_subreddit": "investing_discussion", "hashtags": null, "ticker": "MSFT"}, {"platform": "reddit", "post_id": "reddit_1k51bk7", "title": "How I've been making 10–15% monthly for the past 3 years trading stocks using just one Indicator", "content": "This method is pretty straightforward and comes down to following the rules exactly, using just one indicator: the Stochastic Oscillator.\n\nFirst, open up the indicator tab and add the Stochastic Oscillator. Set it to 5 - 3 - 3 (close/close) and use the 15-minute timeframe.\n\nFor my trading software setup, I use free TradingView Premium from [r/BestTrades](https://www.reddit.com/r/BestTrades/comments/1kcc51e/sharing_free_reverseengineered_tradingview/). It’s an absolute must-have if you're doing serious analysis. They have versions for both Windows and Mac. Having access to more indicators and real-time price data has made a huge difference, and the fact that it’s free is just a bonus. **If you want to use paid version - do it. I am simply sharing what worked for me!**\n\nYou’ll see three zones on the oscillator:\n\n0 to 20 is the oversold zone, meaning the stock is considered too cheap and often signals a good time to buy.  \n80 to 100 is the overbought zone, which usually signals a good spot to sell or look for a short.  \nAnything between 20 and 80 is the neutral zone, and for this strategy we completely ignore it.\n\nNow here’s how I enter trades:\n\nBoth stochastic lines need to fully enter and then exit one of the extreme zones, either overbought or oversold.  \nUse the crosshair to mark where the red signal line crosses out of the zone.  \nWait for two candles in a row that are the same color, green for buys and red for sells.  \nThe wicks on those two candles should be smaller than their bodies. This shows clean price action with momentum.  \nIf everything lines up, I enter the trade at the open of the third candle using shares of the stock.\n\nFor exits, I usually target a 1.5 to 2.5 percent return depending on volatility and how strong the move looks. If momentum stays solid, I might hold a bit longer, but most trades are done within 30 to 60 minutes.\n\nThis works best on large-cap stocks and ETFs with good volume like AAPL, AMD, TSLA, SPY, or QQQ. I’ve used this strategy to consistently make 10 to 15 percent a month on my capital. No tricks or fancy signals, just a simple method, tested over time, and sticking to the rules.\n\nIf you’re curious or not sure, try it out on paper first. That’s how I started before trading live.", "author": "TurbulentKings", "created_time": "2025-04-22T08:30:09", "url": "https://reddit.com/r/investing_discussion/comments/1k51bk7/how_ive_been_making_1015_monthly_for_the_past_3/", "upvotes": 0, "comments_count": 7, "sentiment": "bullish", "engagement_score": 14.0, "source_subreddit": "investing_discussion", "hashtags": null, "ticker": "MSFT"}, {"platform": "reddit", "post_id": "reddit_1k570jf", "title": "Amazon Follows Microsoft in Retreat From Ambitious AI Data Center Plans - Gizmodo", "content": "", "author": "AmazonNewsBot", "created_time": "2025-04-22T14:00:03", "url": "https://reddit.com/r/amazon/comments/1k570jf/amazon_follows_microsoft_in_retreat_from/", "upvotes": 14, "comments_count": 0, "sentiment": "neutral", "engagement_score": 14.0, "source_subreddit": "amazon", "hashtags": null, "ticker": "MSFT"}, {"platform": "reddit", "post_id": "reddit_1k5aa55", "title": "Microsoft website is so slow which makes me want to avoid it", "content": "For many years, I dread going to [microsoft.com](http://microsoft.com) to do anything - everything is so slow. Whether it is accessing account, Xbox or anything else - things are very slow. Will one day in future it will be responsive as many other websites?", "author": "Special-Winner-8591", "created_time": "2025-04-22T16:13:51", "url": "https://reddit.com/r/microsoft/comments/1k5aa55/microsoft_website_is_so_slow_which_makes_me_want/", "upvotes": 0, "comments_count": 1, "sentiment": "neutral", "engagement_score": 2.0, "source_subreddit": "microsoft", "hashtags": null, "ticker": "MSFT"}, {"platform": "reddit", "post_id": "reddit_1k5b5j1", "title": "Microsoft targets ‘low performers’ in a sensational new memo", "content": "", "author": "76willcommenceagain", "created_time": "2025-04-22T16:49:02", "url": "https://reddit.com/r/microsoft/comments/1k5b5j1/microsoft_targets_low_performers_in_a_sensational/", "upvotes": 377, "comments_count": 69, "sentiment": "neutral", "engagement_score": 515.0, "source_subreddit": "microsoft", "hashtags": null, "ticker": "MSFT"}]