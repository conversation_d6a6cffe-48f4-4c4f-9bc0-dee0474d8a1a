[{"platform": "reddit", "post_id": "reddit_1kdj64z", "title": "Looking for encouragement - US Applications", "content": "Hey there everyone. \n\nJust trying to get a feel for if anyone else is experiencing this, and if so if there’s any encouragement from current employees/interviewees. \n\nI had an application open for two full months that I was extremely excited about. Had an internal referral, exceeded all required and preferred qualifications, matched my role and job that I have had for the last 3+ years and am exceeding at (shown through specifications in resume), and I just heard back late last night that I was not selected.\n\n<PERSON><PERSON> very discouraging that after all this time, and feeling very confident about this one, that I was not even reached out to for the first round of potential interviews. \n\nIs the current hiring pool just that large that even in the perfect match roles, there are probably x number of people who are just even slightly better to bump you out of any consideration? \n\nMicrosoft has been my dream company since my first introduction to software engineering in high school over 10 years ago and just can’t seem to make it work. Do you need FAANG experience or building your own suite of apps, services, and garage based nuclear reactors to even be considered? ", "author": "Physical-Mastodon-39", "created_time": "2025-05-03T03:07:12", "url": "https://reddit.com/r/microsoft/comments/1kdj64z/looking_for_encouragement_us_applications/", "upvotes": 3, "comments_count": 19, "sentiment": "neutral", "engagement_score": 41.0, "source_subreddit": "microsoft", "hashtags": null, "ticker": "MSFT"}, {"platform": "reddit", "post_id": "reddit_1kdqxcl", "title": "Phone link - please make it work. -  I have really sharp criticism.", "content": "I have been using phone link since day one. Its an excellent concept that rarely works. Does anyone at Microsoft actually use it to see how bad it is? - I am a huge MS fanboy but this is bad. It cant just be me.", "author": "dialsoft", "created_time": "2025-05-03T11:37:08", "url": "https://reddit.com/r/microsoft/comments/1kdqxcl/phone_link_please_make_it_work_i_have_really/", "upvotes": 2, "comments_count": 12, "sentiment": "neutral", "engagement_score": 26.0, "source_subreddit": "microsoft", "hashtags": null, "ticker": "MSFT"}, {"platform": "reddit", "post_id": "reddit_1kdtkf2", "title": "Customized bot", "content": "Hi,\n\nI’m working on a project for a customer and could use some advice.\n\nThe goal is to build a custom bot using only Microsoft tools. The client has over 700 reference cases—each with metadata like people involved, workdays, pricing, and length—that they want to search and filter easily via natural language questions.\n\nI initially tried using Studio Copilot and uploaded the reference cases there. It works okay, but the issue is that Copilot seems limited to returning only four results at a time, even when more are relevant. The customer needs to see all matching cases, or at least be able to browse/filter through them when asking about certain attributes.\n\nHas anyone tackled something like this with Microsoft’s stack?", "author": "ThrowRA-account09", "created_time": "2025-05-03T13:56:56", "url": "https://reddit.com/r/microsoft/comments/1kdtkf2/customized_bot/", "upvotes": 0, "comments_count": 4, "sentiment": "neutral", "engagement_score": 8.0, "source_subreddit": "microsoft", "hashtags": null, "ticker": "MSFT"}, {"platform": "reddit", "post_id": "reddit_1ke2kyr", "title": "“It’s Not a Bug, It’s a Feature”: Microsoft’s RDP Caching Nightmare", "content": "Old Microsoft Passwords Never Die — They Just Keep Logging In via RDP.\n\nThis sounds like the beginning of a joke, but unfortunately, it’s a real security concern confirmed by Microsoft.\n\nSecurity researcher <PERSON> recently discovered a bizarre behavior in Windows Remote Desktop Protocol (RDP): if you connect to a machine using a Microsoft or Azure account, and then change your password (either for security or routine hygiene), your old password still works — even after the change.\n\nYes, you read that right. Your “retired” password still grants RDP access.\n\n<PERSON>, along with other security professionals like <PERSON> (Analygence), flagged this not just as a bug, but as a [serious breach of trust](https://arstechnica.com/security/2025/04/windows-rdp-lets-you-log-in-using-revoked-passwords-microsoft-is-ok-with-that/). After all, the whole point of changing a password is to revoke access — not keep it alive in the shadows.\n\nSo how does this happen?\nTurns out, when you authenticate with a Microsoft or Azure account via RDP for the first time, Windows performs an online check and then [locally caches encrypted credentials](https://www.techradar.com/pro/security/microsoft-rdp-apparently-lets-you-log-in-with-expired-passwords-but-it-doesnt-plan-to-fix-this). From that point on, RDP reuses the cached credentials to validate access — even if the password was changed in the cloud. In some cases, multiple old passwords may continue to work, while the new one may not yet propagate immediately.\n\nThis mechanism sidesteps:\n\nCloud authentication checks\n\nMulti-Factor Authentication (MFA)\n\nConditional Access Policies\n\n\nAnd Microsoft’s response? The twist: “It’s not a bug, it’s a feature.”\nAccording to them, this is a design decision intended to ensure at least one account can always access the machine, even if it’s offline for extended periods. They confirmed the behavior and updated their documentation — but offered no fix, only a vague suggestion to limit RDP to local accounts, which isn’t very helpful for those relying on Azure/Microsoft accounts.\n\nTL;DR:\nChanging your Microsoft password doesn’t necessarily lock out RDP access with the old one — it lingers, cached and still functional. That “safety feature” might just be a hidden backdoor.\n\nSo next time you change your password and think you’re secure… think again.\n\nMicrosoft?\n", "author": "Echowns", "created_time": "2025-05-03T20:38:49", "url": "https://reddit.com/r/microsoft/comments/1ke2kyr/its_not_a_bug_its_a_feature_microsofts_rdp/", "upvotes": 12, "comments_count": 17, "sentiment": "bullish", "engagement_score": 46.0, "source_subreddit": "microsoft", "hashtags": null, "ticker": "MSFT"}, {"platform": "reddit", "post_id": "reddit_1ke2xt2", "title": "Windows 10 vs Windows 11 - what is your choice?", "content": "Hi guys,\n\nso my Windows 10 decided to upgrade to 11 without warning. I didn't receive any notification but well ok. I was planned to format and install Windows 11 after EOL of 10 so I can skip that for some time :D\n\nSo my main question is: What is your Windows OS choice? 10 or 11? And why?", "author": "PitPlay", "created_time": "2025-05-03T20:55:09", "url": "https://reddit.com/r/microsoft/comments/1ke2xt2/windows_10_vs_windows_11_what_is_your_choice/", "upvotes": 0, "comments_count": 24, "sentiment": "neutral", "engagement_score": 48.0, "source_subreddit": "microsoft", "hashtags": null, "ticker": "MSFT"}]