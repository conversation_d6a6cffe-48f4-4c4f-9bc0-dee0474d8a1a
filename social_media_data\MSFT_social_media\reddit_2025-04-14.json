[{"platform": "reddit", "post_id": "reddit_1jyqvlz", "title": "Please for the love of god stop changing names of things once they’re released!", "content": "I just spent 5 minutes on my iPad looking for my Remote Desktop icon because I couldn’t find it. \n\nTurns out it’s now called “Windows”. \nIt’s not fucking windows, it’s Remote Desktop. \n\nWhy the fuck do they do this? \n\nZune music became Xbox music became groove music. \n\nOffice lens became Microsoft lens.\n\n<PERSON><PERSON><PERSON> became copilot.\n\nAll I know if when I want to do shit I have to search for a new icon and name seemingly every couple of years because they changed it.", "author": "UsidoreTheLightBlue", "created_time": "2025-04-14T04:53:12", "url": "https://reddit.com/r/microsoft/comments/1jyqvlz/please_for_the_love_of_god_stop_changing_names_of/", "upvotes": 263, "comments_count": 53, "sentiment": "neutral", "engagement_score": 369.0, "source_subreddit": "microsoft", "hashtags": null, "ticker": "MSFT"}, {"platform": "reddit", "post_id": "reddit_1jyxvl1", "title": "Are cheap product keys even legit?", "content": "Hi everyone,\n\nI need to get an office license to use word for  some uni releted stuff, but because I'm finishing my studies soon I probably won't need it anymore so I do not want to spend to much on it. Can anyone tell me is it worth bying realy cheap keys I found on internet (≈8-20€) and are they even legit?", "author": "Additional-Gap-5747", "created_time": "2025-04-14T12:41:14", "url": "https://reddit.com/r/microsoft/comments/1jyxvl1/are_cheap_product_keys_even_legit/", "upvotes": 1, "comments_count": 21, "sentiment": "neutral", "engagement_score": 43.0, "source_subreddit": "microsoft", "hashtags": null, "ticker": "MSFT"}, {"platform": "reddit", "post_id": "reddit_1jyxyeg", "title": "I'm starting as an MS employee today. Is it possible to get an SMTP alias for my email address for Contoso or Adatum just for giggles?", "content": "I start today as a senior advanced Azure customer engineer in CXP. Big ups to this subreddit and all the folks who helped me out through the process, I love and appreciate you all!\n\nI've done a bunch of MS certs over the years. The prep materials and exam questions use fictional company names - Contoso, Adatum, and Alpine Ski Lodge are the big ones, IIRC. MS owns Contoso and Adatum. I'd love to be able to give my email <NAME_EMAIL> just as a nice Easter egg for all the exam vets. \n\nDoes MS allow this? If so, does it need an inordinate amount of hoops to jump through and stress on people to carry it out? It's not the end of the world if it's not doable, just a little in-joke to use with other professionals. ", "author": "MohnJaddenPowers", "created_time": "2025-04-14T12:44:58", "url": "https://reddit.com/r/microsoft/comments/1jyxyeg/im_starting_as_an_ms_employee_today_is_it/", "upvotes": 0, "comments_count": 8, "sentiment": "neutral", "engagement_score": 16.0, "source_subreddit": "microsoft", "hashtags": null, "ticker": "MSFT"}, {"platform": "reddit", "post_id": "reddit_1jz1v33", "title": "Microsoft Now Wants Your Credit Score?", "content": "I just saw this email come across my outlook app.  Microsoft \"Defender\" is now offering credit and identity alerts?  We are at hour 11:59 on the privacy and social credit score Doomsday clock.  This is i.n.s.a.n.e!  Am I over reacting? \n\n[https://support.microsoft.com/en-us/topic/credit-monitoring-in-microsoft-defender-faq-9928b902-e7c1-49c6-8e55-39c044cbdf10](https://support.microsoft.com/en-us/topic/credit-monitoring-in-microsoft-defender-faq-9928b902-e7c1-49c6-8e55-39c044cbdf10)", "author": "Flash_Discard", "created_time": "2025-04-14T15:36:06", "url": "https://reddit.com/r/microsoft/comments/1jz1v33/microsoft_now_wants_your_credit_score/", "upvotes": 0, "comments_count": 9, "sentiment": "neutral", "engagement_score": 18.0, "source_subreddit": "microsoft", "hashtags": null, "ticker": "MSFT"}, {"platform": "reddit", "post_id": "reddit_1jz8ikp", "title": "Microsoft product evolutions: New options now that Outlook Rules are basically kaput?", "content": "TL;DR: What options are available in the new Microsoft environment in lieu of the old school email rules for Outlook? \n\nI was recently told to convert to the “New Outlook”. My rules did not transfer with me. \n\nSome googling told me that Microsoft is moving away from email rules and more towards AI-powered products but didn’t give specifics about which products to utilize. \n\nWhen speaking with my IT team, they didn’t really give me any solid solutions other than, “Relearn how to use Microsoft” — but didn’t actually tell me what to start learning in lieu of rules. I’m not in the tech world, so I was generally unaware of this change to Outlook. \n\nI asked ChatGPT, which suggested Power Automate. Is PA the only path forward, then? \n\nThanks for any insight about this. I really want to try to stay current with evolving tech/business trends and understanding the use of AI in business. \n\n\n\n\n\n\n\n", "author": "sunkenspaghetti", "created_time": "2025-04-14T20:03:59", "url": "https://reddit.com/r/microsoft/comments/1jz8ikp/microsoft_product_evolutions_new_options_now_that/", "upvotes": 6, "comments_count": 38, "sentiment": "bullish", "engagement_score": 82.0, "source_subreddit": "microsoft", "hashtags": null, "ticker": "MSFT"}, {"platform": "reddit", "post_id": "reddit_1jzb7b5", "title": "Applied to Microsoft Software Engineering Role and Need some Insight", "content": "Hey everyone, I just applied for a Software Engineering Role. I wanted to share kind of where I am at in the process and if I could get some feedback from hiring managers or some people who went through a similar experience that would be awesome.\n\nSo essentially I sent in my application on Friday the 11th for context, its currently the 14th. I did some viewing and people say it could take anywhere from a few weeks to a few months for anyone to reach out. To put into perspective I applied with a referral and a letter of recommendation with a tailored resume for this position. I fully qualify for the position and as soon as I submit it (Maybe a few hours) the application says \"Transferred\". I have applied for internships before and I they have been labeled \"transferred\" for months with no feedback.\n\nDoes anyone know what my chances are of getting an interview? Another question, in your experience how long has microsoft taken to reach out?\n\nThanks everyone\n\nEdit: I also currently work as a SDET vendor for microsoft, not sure if this helps or hurts my chances. Please let me know also", "author": "Fishin<PERSON>lin", "created_time": "2025-04-14T21:57:00", "url": "https://reddit.com/r/microsoft/comments/1jzb7b5/applied_to_microsoft_software_engineering_role/", "upvotes": 0, "comments_count": 5, "sentiment": "bullish", "engagement_score": 10.0, "source_subreddit": "microsoft", "hashtags": null, "ticker": "MSFT"}, {"platform": "reddit", "post_id": "reddit_1jzcy4p", "title": "What’s the best recent book that covers <PERSON> and/or the rise of Microsoft?", "content": "Been on a deep dive into quite a few corporate history books, but haven’t managed to find one about Gates and Microsoft.\n\nAre there any recent books (maybe like last 5-10 years) that covers this? ", "author": "Maximum_Jello_9460", "created_time": "2025-04-14T23:15:04", "url": "https://reddit.com/r/microsoft/comments/1jzcy4p/whats_the_best_recent_book_that_covers_bill_gates/", "upvotes": 16, "comments_count": 14, "sentiment": "neutral", "engagement_score": 44.0, "source_subreddit": "microsoft", "hashtags": null, "ticker": "MSFT"}]