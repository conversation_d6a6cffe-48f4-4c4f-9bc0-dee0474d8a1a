[{"platform": "reddit", "post_id": "reddit_1ijio7v", "title": "Anyone has an update on the msft software engineer ai/ml role in redmond.", "content": "So im just wondering if Anyone has heard back from Microsoft for the software engineer ai/ml role in redmond. I applied on the 2nd of January as a new grad international based in the uk and was then transferred the next day to another role called FTE SWE AI ML January.\n\nI also managed to get a refferal for the role about a week ago but still havent recieved any type of communication at all not even a phone screen. Is this normal and the wait time could be a up to a month or should I just forget about it.", "author": "Reason-Plenty", "created_time": "2025-02-07T00:56:40", "url": "https://reddit.com/r/microsoft/comments/1ijio7v/anyone_has_an_update_on_the_msft_software/", "upvotes": 1, "comments_count": 9, "sentiment": "neutral", "engagement_score": 19.0, "source_subreddit": "microsoft", "hashtags": null, "ticker": "MSFT"}, {"platform": "reddit", "post_id": "reddit_1ijjk6i", "title": "Microsoft Software Engineer Screening Round Interview Experience", "content": "Hi\n\n\n\nI completed a screening(first) round for IC2 Software Engineer at Microsoft with Hiring Manager. My primary experience is with enterprise software development with Java, but the role is more inclined with Low level programming. \n\n\n\nSo tasked with a couple of coding tasks in C. First one is simple, and I did it pretty quick. But the second one, When I'm implementing the brute force approach by having three loops. The interview mentioned It's better to do it in an optimized way by using a data structure.\n\n\n\nI could not think of this because I was coding with C (When asked about it, I rated myself a 7 in C programming) in the interview when my primary language is Java. Also, my recruiter mentioned me that there would be no coding, it will focus primarily on my resume. So, I prepared only my experience and some concepts. This was also a reason for me not doing well.\n\n\n\nThe interview is scheduled for 45 mins, but it lasted for 30 mins. The interview mentioned the role would be comprising extremely low-level programming. \n\n\n\nSo, how long should I wait for the result. Do you think I would clear this round? I'm not sure about the standards of Microsoft. I have a feeling that I won't clear this tbh.\n\n\n\nThanks in advance.", "author": "sutbborn_face", "created_time": "2025-02-07T01:40:23", "url": "https://reddit.com/r/microsoft/comments/1ijjk6i/microsoft_software_engineer_screening_round/", "upvotes": 0, "comments_count": 1, "sentiment": "bullish", "engagement_score": 2.0, "source_subreddit": "microsoft", "hashtags": null, "ticker": "MSFT"}, {"platform": "reddit", "post_id": "reddit_1ijvlty", "title": "Amazon, Echoing Microsoft, Says It Can't Keep Up With AI Demand - Bloomberg", "content": "", "author": "AmazonNewsBot", "created_time": "2025-02-07T14:00:04", "url": "https://reddit.com/r/amazon/comments/1ijvlty/amazon_echoing_microsoft_says_it_cant_keep_up/", "upvotes": 3, "comments_count": 0, "sentiment": "neutral", "engagement_score": 3.0, "source_subreddit": "amazon", "hashtags": null, "ticker": "MSFT"}, {"platform": "reddit", "post_id": "reddit_1ijwcmm", "title": "What are the upgraded features of purchased office programs?", "content": "This may be a stupid question but honestly, I don’t know what “purchased” word, powerpoint, or excel do that is better than the free apps. Can someone give me a breakdown or a link to somewhere where this is explained?", "author": "chickenfeeder41", "created_time": "2025-02-07T14:34:46", "url": "https://reddit.com/r/microsoft/comments/1ijwcmm/what_are_the_upgraded_features_of_purchased/", "upvotes": 1, "comments_count": 1, "sentiment": "neutral", "engagement_score": 3.0, "source_subreddit": "microsoft", "hashtags": null, "ticker": "MSFT"}, {"platform": "reddit", "post_id": "reddit_1ik7gik", "title": "What to expect in my first connect as an L63 Sr SDE?", "content": "Hello All,\n\nI joined Microsoft as an L63 Sr SDE in September in the C+E org. So far things have ben stressful, microsoft doesn't really have an onboarding bootcamp, so I feel like I am kind of expected to ramp up very fast and deliver on my sprint tasks.\n\nI feelI am doing okay. I was also a full stack developer role, but I got reorged into a data engineering role. For example, I was building typescript/react based apps and maintaining backends also in same language. My new team mostly runs jobs on Azure Data FActory, and uses Scala, and Spark, big mindset shift for me. The tasks are coming top down, and i find myself working on weekeends as the estimates are assigned by the manager.\n\nThe Nov connect was really looking forward, and was more of a mock connect if you will. My real connect will be in May/June I think, what are the odds that they give me a LITE or 80% in the first connect?\n\nI don't have a lot of faith in my manager, as she has a poker face. I am aslo struggling because my team  and manager included have a very strong accent that I cannot understand even after watching the recorded videos.\n\nThanks", "author": "PartySuccotash5011", "created_time": "2025-02-07T22:20:28", "url": "https://reddit.com/r/microsoft/comments/1ik7gik/what_to_expect_in_my_first_connect_as_an_l63_sr/", "upvotes": 13, "comments_count": 18, "sentiment": "bullish", "engagement_score": 49.0, "source_subreddit": "microsoft", "hashtags": null, "ticker": "MSFT"}]