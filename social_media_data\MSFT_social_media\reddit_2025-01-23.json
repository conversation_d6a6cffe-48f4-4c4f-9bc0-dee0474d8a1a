[{"platform": "reddit", "post_id": "reddit_1i7u3c0", "title": "Microsoft's business development chief <PERSON> resigns", "content": "", "author": "ControlCAD", "created_time": "2025-01-23T03:18:45", "url": "https://reddit.com/r/microsoft/comments/1i7u3c0/microsofts_business_development_chief_ch<PERSON>_young/", "upvotes": 387, "comments_count": 29, "sentiment": "neutral", "engagement_score": 445.0, "source_subreddit": "microsoft", "hashtags": null, "ticker": "MSFT"}, {"platform": "reddit", "post_id": "reddit_1i7vmsn", "title": "Remote Positions - 50 Mile Hub Radius?", "content": "Straight to the point question I have here. I live in southern FL and was looking at quite a few Microsoft positions that state up to 100% remote, but sometimes in the job descriptions I'm seeing you have to be within a 50 mile radius of a hub or it just doesn't say it at all. The jobs I'm gearing towards (Procurement/Supply Chain) are stating relocation will be supported to be within 50 mile radius. I have a family and pretty much have no intention of wanting to move anywhere, is it even worth me applying to these positions if this is actually true? I'm seeing on Reddit some people aren't even actually within that radius and have negotiated with the hiring manager? ", "author": "TrumpBrahs", "created_time": "2025-01-23T04:43:41", "url": "https://reddit.com/r/microsoft/comments/1i7vmsn/remote_positions_50_mile_hub_radius/", "upvotes": 3, "comments_count": 2, "sentiment": "neutral", "engagement_score": 7.0, "source_subreddit": "microsoft", "hashtags": null, "ticker": "MSFT"}, {"platform": "reddit", "post_id": "reddit_1i7webz", "title": "Copilot integration with Microsoft word is now free?", "content": "I have notice when Im using microsoft word, the copilot icon is already popping up. But i havent used it yet. Does anyone is using this feature in microsoft word?", "author": "SilentAdvocate2023", "created_time": "2025-01-23T05:30:16", "url": "https://reddit.com/r/microsoft/comments/1i7webz/copilot_integration_with_microsoft_word_is_now/", "upvotes": 3, "comments_count": 5, "sentiment": "neutral", "engagement_score": 13.0, "source_subreddit": "microsoft", "hashtags": null, "ticker": "MSFT"}, {"platform": "reddit", "post_id": "reddit_1i7xrxw", "title": "Remote employee question", "content": "Hi! I recently was offered a job at Microsoft and my background check just completed, so I haven’t started yet. I’m not located in WA and my job is 100% remote. Im just curious—if there is a Microsoft office near me, can I go in and use it on random days if I want? Or is it only for those assigned to teams that work out of those buildings?\n\nI’ve been remote since 2018 and it can get a little isolating/stir crazy, so would love the option to go somewhere, even for a few hours.\n\nCheers! ", "author": "Katinthehat02", "created_time": "2025-01-23T07:02:59", "url": "https://reddit.com/r/microsoft/comments/1i7xrxw/remote_employee_question/", "upvotes": 8, "comments_count": 24, "sentiment": "neutral", "engagement_score": 56.0, "source_subreddit": "microsoft", "hashtags": null, "ticker": "MSFT"}, {"platform": "reddit", "post_id": "reddit_1i7z7wu", "title": "Cheaper hidden Office offer without Copilot", "content": "I am (was, actually) a Microsoft 365 Family subscriber. A few days ago Copilot started showing up integrated into my Office apps. I am not a big fan of Microsoft shoving and trying to upsell Copilot everywhere, but I have grown accostumed to it and I thought no more of the subject. However, a few days after that, when browsing through my Microsoft account settings, I accidentally discovered that for the \"privilege\" of having Copilot unwillingly shoved into my face, my subscription is now 30% (!!!) more expensive. Fortunately I was on an annual subscription and thus rebilling was still a few months away!\n\nAfter pondering for a while, I decided to eradicate the problem at the source and canceled my subscription. I discovered, and want to share with potential users facing the same dilemma, that when you are canceling your subscription, you are presented with a hidden, 30% cheaper M365 subscription without Copilot. That offer is not accessible anywhere outside the cancelation screen. \n\n This \"hidden offer when canceling\" seems right out of the playbook of unreputable adult content merchants rather than reputable software vendors. It was a bit too shady for me and so I've moved on to a non-Microsoft office product, but for those interested in getting the non-Copilot version of Office at the previous price, take note of the existence of this hidden offer.  \n\n In my opinion, Microsoft COULD have been user friendly when selling these features to users. For example: \n\n* \"We have integrated Copilot into Office. Do you want to try it out? Click here to activate a 15-day trial. If you like it, you can enable the Copilot add-on for your subscription for a 30% price increase. You can also disable this add-on in the future at any time and it will take effect at the next time your subscription renews.\" \n* New subscribers of M365 can choose to subscribe with or without the Copilot add-on. \n\nWhat  Microsoft actually chose to do: \n\n* Everyone gets a silent 30% price increase. Users do not get notified of the price increase. There is no explanation of the reason behind the price increase (Copilot integration). \n* Copilot gets forcefully shoved in everyone's face inside Office. (The OneNote integration is specially disgusting, the Copilot icon literally follows your cursor around everywhere.) There is no way to disable it except for using a global setting for \"connected experiences\" - which disables a lot more than just Copilot. \n* They implemented a hidden offer without Copilot integration and the associated price increase that is kept secret and only presented to users when canceling their subscription as a last resort customer retention tactic - because they KNEW perfectly well that some subscribers would cancel after such a gigantic price increase. ", "author": "forger-eight", "created_time": "2025-01-23T08:54:19", "url": "https://reddit.com/r/microsoft/comments/1i7z7wu/cheaper_hidden_office_offer_without_copilot/", "upvotes": 35, "comments_count": 42, "sentiment": "bearish", "engagement_score": 119.0, "source_subreddit": "microsoft", "hashtags": null, "ticker": "MSFT"}, {"platform": "reddit", "post_id": "reddit_1i83ysc", "title": "Amazon UK Boss Says Mr <PERSON> Vs The Post Office Wouldn't Work On Prime - Deadline", "content": "", "author": "AmazonNewsBot", "created_time": "2025-01-23T14:00:04", "url": "https://reddit.com/r/amazon/comments/1i83ysc/amazon_uk_boss_says_mr_bates_vs_the_post_office/", "upvotes": 2, "comments_count": 0, "sentiment": "neutral", "engagement_score": 2.0, "source_subreddit": "amazon", "hashtags": null, "ticker": "MSFT"}]