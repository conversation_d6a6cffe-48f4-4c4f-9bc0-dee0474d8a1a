[{"platform": "reddit", "post_id": "reddit_1k63okw", "title": "Sr Product Designer Salary @ Microsoft India?", "content": "Hi, I'm in the final stage of interviewing for the role of a Senior Product Designer, based in India. The job description mentions having 8/10+ years of experience. \nWhat should be the expected salary range for this role during negotiation? Any inputs appreciated!", "author": "PianistOk509", "created_time": "2025-04-23T16:34:55", "url": "https://reddit.com/r/microsoft/comments/1k63okw/sr_product_designer_salary_microsoft_india/", "upvotes": 0, "comments_count": 2, "sentiment": "bearish", "engagement_score": 4.0, "source_subreddit": "microsoft", "hashtags": null, "ticker": "MSFT"}, {"platform": "reddit", "post_id": "reddit_1k63zkh", "title": "Dear Microsoft . . .", "content": "You give us features we didn't know we needed, that will save us life's most valuable resource -- time -- but you then you break basic features, and we spend scads of life's most valuable resource trying to fix what you've broken. Stop it!\n\nAddendum: I'm frustrated today with the New Outlook, changes to Teams, Copilot Studay, Power Apps, and Windows 11... and it's only noon.\n\nAddendum 2: It wouldn't be so bad if this happened in just one product, but when it happens in all of the user products in a constant deluge of changes, it's impossible to keep up. Not to mention the changes in Azure et al every day.", "author": "mind-meld224", "created_time": "2025-04-23T16:47:26", "url": "https://reddit.com/r/microsoft/comments/1k63zkh/dear_microsoft/", "upvotes": 186, "comments_count": 46, "sentiment": "neutral", "engagement_score": 278.0, "source_subreddit": "microsoft", "hashtags": null, "ticker": "MSFT"}, {"platform": "reddit", "post_id": "reddit_1k693vr", "title": "Data Center Technician Manager interview", "content": "Hi all! I currently work as a Technical Account Manager / Cloud Architect at AWS with Data Center experience, and I just noticed an opening for a Data Center Technician Manager role.\n\nMy questions are:\n\n* Is this a good role? I can't understand if this is pure manager role or a mix.\n* How doable is to move internally later on to, for example, a Solutions Architect role if I see that would be a better fit?\n* I remember some years ago having a conversation with a recruiter for a DC Technician role at Microsoft and the salary was not very high comparing to AWS, no stocks whatsoever, does the same applies to this manager role?\n\nMy biggest concern is if I'm taking a step back in my career by moving to this role.\n\nEdit: Also, what is the career progression for this role?  \nEdit 2: My main motivator is because I want to move for a management role.", "author": "Kratus7", "created_time": "2025-04-23T20:12:45", "url": "https://reddit.com/r/microsoft/comments/1k693vr/data_center_technician_manager_interview/", "upvotes": 0, "comments_count": 6, "sentiment": "neutral", "engagement_score": 12.0, "source_subreddit": "microsoft", "hashtags": null, "ticker": "MSFT"}]