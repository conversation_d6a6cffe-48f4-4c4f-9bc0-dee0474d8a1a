[{"platform": "reddit", "post_id": "reddit_1jj8zho", "title": "What to expect for your first week at Microsoft?", "content": "I haven't been in touch since I received my start date (3/20). My start date is 3/31, should I expect to get my equipment before my start date? Will I receive instructions for the first day/week sometime this week? Should I just reach out to my hiring manager? Any tips or advice is welcomed. ", "author": "rusty919", "created_time": "2025-03-25T02:20:50", "url": "https://reddit.com/r/microsoft/comments/1jj8zho/what_to_expect_for_your_first_week_at_microsoft/", "upvotes": 6, "comments_count": 46, "sentiment": "neutral", "engagement_score": 98.0, "source_subreddit": "microsoft", "hashtags": null, "ticker": "MSFT"}, {"platform": "reddit", "post_id": "reddit_1jji112", "title": "CoPilot = Microsoft just cant get it right", "content": "Copilot on my Windows PC was working great at first. Could talk with it using a microphone and have a semi natural conversation. A few months ago, an update must have occurred and that  stopped working. Could only txt chat with it via the keyboard. \n\nI do have a 365 business license which seems to have caused some conflicts. But why would a free MS Live account have better copilot features than any PAID 365 license? \n\nNow today it will not launch at all. \n\nWhat's going on with MS Copilot?  Too many cooks at the kitchen in MS or are they just unable to do anything right?", "author": "captain_222", "created_time": "2025-03-25T12:20:49", "url": "https://reddit.com/r/microsoft/comments/1jji112/copilot_microsoft_just_cant_get_it_right/", "upvotes": 0, "comments_count": 4, "sentiment": "neutral", "engagement_score": 8.0, "source_subreddit": "microsoft", "hashtags": null, "ticker": "MSFT"}, {"platform": "reddit", "post_id": "reddit_1jji2v5", "title": "Microsoft AI Tour Resources Now Available for public use", "content": "", "author": "Wireless_Life", "created_time": "2025-03-25T12:23:37", "url": "https://reddit.com/r/microsoft/comments/1jji2v5/microsoft_ai_tour_resources_now_available_for/", "upvotes": 6, "comments_count": 1, "sentiment": "neutral", "engagement_score": 8.0, "source_subreddit": "microsoft", "hashtags": null, "ticker": "MSFT"}, {"platform": "reddit", "post_id": "reddit_1jjs46o", "title": "How a Microsoft exec managed to pitch Microsoft Word through the genius tactic of being able to actually use it in a 'type-off' demanded by clients: 'I was the only one who'd actually been a secretary'", "content": "", "author": "ControlCAD", "created_time": "2025-03-25T19:35:55", "url": "https://reddit.com/r/microsoft/comments/1jjs46o/how_a_microsoft_exec_managed_to_pitch_microsoft/", "upvotes": 170, "comments_count": 2, "sentiment": "neutral", "engagement_score": 174.0, "source_subreddit": "microsoft", "hashtags": null, "ticker": "MSFT"}, {"platform": "reddit", "post_id": "reddit_1jjur9m", "title": "Microsoft Designer got worse", "content": "If the usage limit wasn't enough today I find it is ignoring the image size specification and always creating square even if set to landscape or portrait.\n\nPlus usual moan about filtering...", "author": "Mysterious_Peak_6967", "created_time": "2025-03-25T21:22:40", "url": "https://reddit.com/r/microsoft/comments/1jjur9m/microsoft_designer_got_worse/", "upvotes": 9, "comments_count": 4, "sentiment": "neutral", "engagement_score": 17.0, "source_subreddit": "microsoft", "hashtags": null, "ticker": "MSFT"}]