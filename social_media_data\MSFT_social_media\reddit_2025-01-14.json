[{"platform": "reddit", "post_id": "reddit_1i0sp54", "title": "Internship: Waitlist Microsoft", "content": "Waitlisted and told \"eligible for placement on a new team if headcount becomes available within the next 6 months.\" Is it likely for people to get off the waitlist at Microsoft? ", "author": "applesandbananasbaby", "created_time": "2025-01-14T00:02:29", "url": "https://reddit.com/r/microsoft/comments/1i0sp54/internship_waitlist_microsoft/", "upvotes": 2, "comments_count": 6, "sentiment": "neutral", "engagement_score": 14.0, "source_subreddit": "microsoft", "hashtags": null, "ticker": "MSFT"}, {"platform": "reddit", "post_id": "reddit_1i11elf", "title": "Customer Success Account Specialist", "content": "Hey everyone! \n\nI have been shortlisted to the CSAS role in Microsoft and would like to understand better the responsibilities of the role. I went through the job description and it kinda sounds like a salesman kind of role instead of a consulting based role. I had a call with the recruiter and he mentioned that there are pre-sales consulting and post sales consulting and this role would be post sales where you would liase with the client and the implementation team to ensure the project goes smoothly. \n\n  \nWould like to hear your thoughts or experience on this! Thanks", "author": "russell616", "created_time": "2025-01-14T08:15:08", "url": "https://reddit.com/r/microsoft/comments/1i11elf/customer_success_account_specialist/", "upvotes": 0, "comments_count": 7, "sentiment": "bearish", "engagement_score": 14.0, "source_subreddit": "microsoft", "hashtags": null, "ticker": "MSFT"}, {"platform": "reddit", "post_id": "reddit_1i12dcs", "title": "Microsoft streaming technology ", "content": "Hello, I wonder if anyone in this group remember a couple of old tech demos that Microsoft showed off, many years ago? I would like to refresh my memory on what the technology was called.\n[I thought that it was called \"Sailfish\" or something similar, but when I search for it, there is only an operative system that shows up...]\n\nIt was a streaming technology that allowed you to display pictures over a slow dial up connection.\n\nIn one demo, you saw what looked like a bunch of different colored squares, but then they zoomed in and you could see that they where many high resolution pictures. I think they did something similar with a text (declaration of independence?).\n\nI believe they worked on the technology for a couple of years and in a later demo, they showed off something similar to Google Street View. Where they had a 360 view of a famous location and they streamed in pictures taken by tourists from that location. As you moved around the pictures came into view. And they used the metadata to show dem in the right location, in the right direction (and angle?).\n\n", "author": "Cane_P", "created_time": "2025-01-14T09:29:16", "url": "https://reddit.com/r/microsoft/comments/1i12dcs/microsoft_streaming_technology/", "upvotes": 1, "comments_count": 2, "sentiment": "neutral", "engagement_score": 5.0, "source_subreddit": "microsoft", "hashtags": null, "ticker": "MSFT"}, {"platform": "reddit", "post_id": "reddit_1i12ho4", "title": "True up License Windows 10 / 11", "content": "m pretty sure im not the only one here with the unpleasant Windows 10 to 11 Migration.\n\nI have question Regarding these True Up-Licenses.\n\nAre they granted for update or do we have to buy some kind of add-on to migrate? Microsoft is bich and we all know that.\n\nTY guys", "author": "SrSFlX", "created_time": "2025-01-14T09:38:44", "url": "https://reddit.com/r/microsoft/comments/1i12ho4/true_up_license_windows_10_11/", "upvotes": 0, "comments_count": 4, "sentiment": "bullish", "engagement_score": 8.0, "source_subreddit": "microsoft", "hashtags": null, "ticker": "MSFT"}, {"platform": "reddit", "post_id": "reddit_1i12oks", "title": "M365", "content": "So it is m365.office.com in the future but is the copilot integrated into the products automatically like Google or do I have to purchase it extra like it is now if I want to use it in the O365 products? I'm heavily using onenote and copilot would be a great addition. Thanks", "author": "deleted", "created_time": "2025-01-14T09:53:22", "url": "https://reddit.com/r/microsoft/comments/1i12oks/m365/", "upvotes": 1, "comments_count": 3, "sentiment": "neutral", "engagement_score": 7.0, "source_subreddit": "microsoft", "hashtags": null, "ticker": "MSFT"}]