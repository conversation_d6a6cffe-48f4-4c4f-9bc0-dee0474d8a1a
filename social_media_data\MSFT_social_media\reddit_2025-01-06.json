[{"platform": "reddit", "post_id": "reddit_1humvtj", "title": "Army to Microsoft ", "content": "Hi everyone! I’m getting out of the army soon and I’d really like to get employed with Microsoft. I’m currently obtaining my net and sec+ certs and my background is 3 years as an army intelligence mission manager for a 3 letter government agency. What I’d really like to know is what jobs at Microsoft I should be looking for. From what I’ve seen entry level positions are few and far between so if anyone has any recommendations as to how I can make myself stand out and what I should be looking for that would be awesome. Thanks!", "author": "Donaldthecriminal", "created_time": "2025-01-06T01:10:57", "url": "https://reddit.com/r/microsoft/comments/1humvtj/army_to_microsoft/", "upvotes": 41, "comments_count": 9, "sentiment": "neutral", "engagement_score": 59.0, "source_subreddit": "microsoft", "hashtags": null, "ticker": "MSFT"}, {"platform": "reddit", "post_id": "reddit_1husrr0", "title": "Pros and cons of SWE and PM at Microsoft?", "content": "I have to opportunity to go down either path and would love to hear about the work, WLB, promotions, career growth, and anything else that might be helpful to me making a decision. Thank you!", "author": "tetracell_", "created_time": "2025-01-06T06:25:44", "url": "https://reddit.com/r/microsoft/comments/1husrr0/pros_and_cons_of_swe_and_pm_at_microsoft/", "upvotes": 0, "comments_count": 15, "sentiment": "bullish", "engagement_score": 30.0, "source_subreddit": "microsoft", "hashtags": null, "ticker": "MSFT"}, {"platform": "reddit", "post_id": "reddit_1huv5wk", "title": "Migrating GoDaddy Mail Services to Microsoft Outlook", "content": "I'm looking to migrate my GoDaddy mail services to Microsoft Outlook(using Microsoft for Startups credit) and could use some guidance. Does anyone have experience with this process or know the best way to go about it?\n\nThanks in advance.", "author": "Main_Helicopter9096", "created_time": "2025-01-06T09:21:14", "url": "https://reddit.com/r/microsoft/comments/1huv5wk/migrating_godaddy_mail_services_to_microsoft/", "upvotes": 2, "comments_count": 2, "sentiment": "neutral", "engagement_score": 6.0, "source_subreddit": "microsoft", "hashtags": null, "ticker": "MSFT"}, {"platform": "reddit", "post_id": "reddit_1huzmvy", "title": "Internal Hiring Bias", "content": "I'm an internal employee and I'm applying for roles within MSFT. I love the company and the culture but there seems to be an internal hiring bias and I'm witnessing it to be easier to leave the company and come back to the role you want to be at. I want to be wrong, am I?", "author": "Zestyclose_Depth_196", "created_time": "2025-01-06T13:59:34", "url": "https://reddit.com/r/microsoft/comments/1huzmvy/internal_hiring_bias/", "upvotes": 0, "comments_count": 12, "sentiment": "neutral", "engagement_score": 24.0, "source_subreddit": "microsoft", "hashtags": null, "ticker": "MSFT"}, {"platform": "reddit", "post_id": "reddit_1hv05hm", "title": "Microsoft Bing shows misleading Google-like page for 'Google' searches", "content": "", "author": "<PERSON><PERSON>", "created_time": "2025-01-06T14:24:00", "url": "https://reddit.com/r/microsoft/comments/1hv05hm/microsoft_bing_shows_misleading_googlelike_page/", "upvotes": 0, "comments_count": 11, "sentiment": "neutral", "engagement_score": 22.0, "source_subreddit": "microsoft", "hashtags": null, "ticker": "MSFT"}, {"platform": "reddit", "post_id": "reddit_1hv3bq4", "title": "Microsoft multilingual glossary?", "content": "I use different language packs at home and at work. Is there a reference somewhere, where I can check the specific translation Microsoft uses for important words in their terminology? (an example : what is the 'official' French equivalent for \"Windows product key\"?)", "author": "Anycauli", "created_time": "2025-01-06T16:42:29", "url": "https://reddit.com/r/microsoft/comments/1hv3bq4/microsoft_multilingual_glossary/", "upvotes": 1, "comments_count": 5, "sentiment": "bearish", "engagement_score": 11.0, "source_subreddit": "microsoft", "hashtags": null, "ticker": "MSFT"}]