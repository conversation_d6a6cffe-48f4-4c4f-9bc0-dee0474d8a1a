[{"platform": "reddit", "post_id": "reddit_1io7rlo", "title": "Thinking of buying Microsoft Home and Business and not 365 because I don`t wanna buy a subscription!", "content": "I am thinking of spending the 250$ to buy office 2024 for home and Bussiness but at the same time I feel its a waist I do  have a windows laptop although. I use but I noticed I use my linux machines more then I do my windows. And I am fine and happy with using LibreOffice which is free is their a reason why using office is better then using say something like LibreOffice?", "author": "RecentMonk1082", "created_time": "2025-02-13T01:35:40", "url": "https://reddit.com/r/microsoft/comments/1io7rlo/thinking_of_buying_microsoft_home_and_business/", "upvotes": 32, "comments_count": 42, "sentiment": "bullish", "engagement_score": 116.0, "source_subreddit": "microsoft", "hashtags": null, "ticker": "MSFT"}, {"platform": "reddit", "post_id": "reddit_1iojcs9", "title": "Onedrive had ruined my personal files!", "content": "A few days ago my system was automatically updated and when I logged in it suggested me to use onedrive to backup things and I just skipped it. But today I found my personal files was uploaded to the onedrive, I don't know how but I must had been induced or deceived to allow it to do so.\n\nThe important thing is, I am using onedrive as a tool to sync my working documents, and created a folder named \"Documents\" to save all my work data. Then I found onedrive have uploaded ALL OF MY FILES IN MY PC'S DOCUMENTS FOLDER, and fixed 'em together with my work data. Of course I don't want my personal files in my work space, I delete them immediatelly from onedrive. But then I realized ONEDRIVE WAS NOT JUST BACKED MY FILES UP, IT JUST MOVED THE ENTIRE DOCUMENTS FOLDER OF MY PC. So when I deleted them from onedrive, I deleted them from everywhere. \n\nThis is so frustrating and makes me extrmely angry, the data I had lost contains so many things for the past decade. And it shouldn't be like this. Microsoft shouldn't be so urgently promoting people to use onedrive while not refining related measures. It should have been a lot more easier to stop the backup progress without any concern of data loss. And people should be informed that when their files were backed up  through onedrive they would become THE ONE AND THE ONLY COPY. Once you delete them from onedrive, you lost them forever.\n\n\n\nP.S. As there're countless softwares in the PC using the Documents folder as their caching space, it's definitely the worst idea you guys have made up to redirect the path of the Documents folder into OneDrive. While my apps are running, Onedrive just won't stop uploading those cached files and running out all my RAMs, it makes my PC stuck as hell. This is so stupid. Please stop this non-sense.", "author": "NeXagoS", "created_time": "2025-02-13T13:45:28", "url": "https://reddit.com/r/microsoft/comments/1iojcs9/onedrive_had_ruined_my_personal_files/", "upvotes": 1, "comments_count": 19, "sentiment": "bearish", "engagement_score": 39.0, "source_subreddit": "microsoft", "hashtags": null, "ticker": "MSFT"}, {"platform": "reddit", "post_id": "reddit_1ion2q3", "title": "You Should Install This Windows Security Patch Right Away", "content": "", "author": "sparkblue", "created_time": "2025-02-13T16:32:38", "url": "https://reddit.com/r/microsoft/comments/1ion2q3/you_should_install_this_windows_security_patch/", "upvotes": 15, "comments_count": 11, "sentiment": "neutral", "engagement_score": 37.0, "source_subreddit": "microsoft", "hashtags": null, "ticker": "MSFT"}, {"platform": "reddit", "post_id": "reddit_1ioo2g8", "title": "Unexpected confidence boost", "content": "Just realized the hazard ⚠️ icon by my profile pic in PowerPoint makes me feel badass.  Love it!   Thank you Microsoft! ", "author": "MissMewMews", "created_time": "2025-02-13T17:13:52", "url": "https://reddit.com/r/microsoft/comments/1ioo2g8/unexpected_confidence_boost/", "upvotes": 0, "comments_count": 2, "sentiment": "neutral", "engagement_score": 4.0, "source_subreddit": "microsoft", "hashtags": null, "ticker": "MSFT"}]