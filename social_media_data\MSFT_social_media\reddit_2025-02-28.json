[{"platform": "reddit", "post_id": "reddit_1izvizu", "title": "Why doesn't Teams offer the option of always muting the mic by default when joining a meeting?", "content": "It is highly frustrating that Microsoft Teams still does not offer the option to mute the microphone by default when joining a meeting. Many other products provide this functionality, yet despite years of user requests, Microsoft has not implemented what is clearly a simple and logical feature. \n\nhttps://answers.microsoft.com/en-us/msteams/forum/all/teams-mute-by-default-when-entering-a-meeting/979e5882-3ffa-45f2-8436-e996056d1d5d?page=18", "author": "RevolutionStill4284", "created_time": "2025-02-28T00:56:25", "url": "https://reddit.com/r/microsoft/comments/1izvizu/why_doesnt_teams_offer_the_option_of_always/", "upvotes": 9, "comments_count": 20, "sentiment": "neutral", "engagement_score": 49.0, "source_subreddit": "microsoft", "hashtags": null, "ticker": "MSFT"}, {"platform": "reddit", "post_id": "reddit_1j02nmz", "title": "AI/ML role interview process", "content": "Can anyone share your interview experience on AI/ML roles at MSFT?\n\nI just passed the initial screening, but my recruiter hasn’t responded to me yet regarding the interview process.\n\nI appreciate your help.", "author": "Blasphemer666", "created_time": "2025-02-28T07:58:03", "url": "https://reddit.com/r/microsoft/comments/1j02nmz/aiml_role_interview_process/", "upvotes": 1, "comments_count": 1, "sentiment": "neutral", "engagement_score": 3.0, "source_subreddit": "microsoft", "hashtags": null, "ticker": "MSFT"}, {"platform": "reddit", "post_id": "reddit_1j039cn", "title": "Microsoft preparing to shut down Skype in May", "content": "", "author": "digidude23", "created_time": "2025-02-28T08:42:39", "url": "https://reddit.com/r/microsoft/comments/1j039cn/microsoft_preparing_to_shut_down_skype_in_may/", "upvotes": 248, "comments_count": 47, "sentiment": "neutral", "engagement_score": 342.0, "source_subreddit": "microsoft", "hashtags": null, "ticker": "MSFT"}, {"platform": "reddit", "post_id": "reddit_1j0ao8d", "title": "Outlook e-mail ads are a security vulnerability", "content": "I'm mind blown by the fact that Microsoft has an obvious security vul in their Outlook app.\n\n  \nAre you guys braindead over there or what?\n\n  \nThis is clearly a security issue waiting to be exploit. Remove the ad as an e-mail function from your app please.", "author": "EntertainmentOk356", "created_time": "2025-02-28T15:45:48", "url": "https://reddit.com/r/microsoft/comments/1j0ao8d/outlook_email_ads_are_a_security_vulnerability/", "upvotes": 0, "comments_count": 5, "sentiment": "neutral", "engagement_score": 10.0, "source_subreddit": "microsoft", "hashtags": null, "ticker": "MSFT"}, {"platform": "reddit", "post_id": "reddit_1j0hc2q", "title": "Microsoft O365 GCC vs GCC High", "content": "We have a client who we are working with on CMMC level 2. We were going to move them to Microsoft GCC but they want to move to GCC high due to potentially having vendors sending ITAR data to them through email. We are having a hard time finding what the restrictions are when it comes to GCC High. One that I'm pretty sure of (But correct me if I'm wrong) is that any enterprise apps that you want to add have to be FedRAMP authorized or you wont be able to add them. This is a fairly big issue since we can't tie in a lot of security services like SIEM/SOC, Reporting, tickets, etc. But overall this limitation would make sense from a security perspective. If its not that case that would be a completely different story.\n\n  \nI know there's other limitations when it comes to stuff like sharing which I'm not overly concerned about. But it's all of the other potential limitations I'm hoping people can shed light on compared to what GCC or even a normal Microsoft tenant has that they don't where its a pain.", "author": "BrandonSB2", "created_time": "2025-02-28T20:23:49", "url": "https://reddit.com/r/microsoft/comments/1j0hc2q/microsoft_o365_gcc_vs_gcc_high/", "upvotes": 2, "comments_count": 4, "sentiment": "neutral", "engagement_score": 10.0, "source_subreddit": "microsoft", "hashtags": null, "ticker": "MSFT"}]