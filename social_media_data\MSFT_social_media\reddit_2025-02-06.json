[{"platform": "reddit", "post_id": "reddit_1iis2li", "title": "Microsoft interview 29th Jan intern results?", "content": "It has been exactly week since my final round at Microsoft for Redmond. From what I heard and seen on other subs if accepted a change in portal would appear to completed. Has anyone that interviewed on 1/29 received that portal status changed yet? For explore or normal swe intern? Mine is still on scheduling, I will wait until the end of the week for better confirmation. Thx", "author": "Creative-Hunter8009", "created_time": "2025-02-06T02:32:01", "url": "https://reddit.com/r/microsoft/comments/1iis2li/microsoft_interview_29th_jan_intern_results/", "upvotes": 5, "comments_count": 5, "sentiment": "neutral", "engagement_score": 15.0, "source_subreddit": "microsoft", "hashtags": null, "ticker": "MSFT"}, {"platform": "reddit", "post_id": "reddit_1ij5pj4", "title": "Updated Privacy Policy should be illegal", "content": "**TLDR:  If you opt out of Microsoft's new connected services you can't sync with OneDrive but if you opt in to connected services, Microsoft will scrape all of your content for AI use.**\n\nSo - I saw a few posts online recently about the updated privacy policy and this new \"connected services\" malarkey.  And of course I went down a rabbit hole - that I wish I hadn't.  \n\nHere's the gist of it.  If you have all of the 3 connected services options selected   \n  \n1) Turn on experiences that analyze your content   \n2) Turn on experiences that download online content   \n3) Turn on all connected experiences.  \n  \n\\- you have zero privacy, and Microsoft WILL use AI to scrape your content.  Even though they say they don't - who are we actually kidding?  The lay language in the privacy policy says certain features like **Microsoft 365 Copilot** do access and process your data to provide ***\"assistance\"***. Copilot connects large language models to your organizational data, including documents, emails, and meetings, to generate contextually relevant responses. (uh-huh).  This processing is done to ***assist*** you and is not used to train Microsoft's AI models.  \n\nYou can choose to disconnect them but here's what will happen if you do.  For example, I want to ensure that my OneNote syncs across my devices.  If I opt out of #3 above (turn on all connected experiences) this is what Microsoft says will happen:\n\n*Cloud-based features like* ***OneNote syncing, real-time collaboration in Word/Excel, and automatic saving to OneDrive*** *will* ***not work****.*\n\nMicrosoft explicitly states that disabling these services means you lose access to any feature that requires cloud connectivity, including:\n\n* ***OneNote cloud sync*** *(your notes will only be stored locally)*\n* ***Auto-save & real-time collaboration*** *in Word, Excel, and PowerPoint*\n* ***Online templates, stock images, and AI-powered tools*** *like Editor or Designer*\n\n*If you want* ***OneNote to sync across devices****, you must enable at least some level of connected services. You can still use* ***OneNote locally*** *without syncing if you keep cloud features disabled.*\n\n  \nNow - you would think that ok - I'll just use option #2 because it allows you to sync OneNote with the cloud while keeping things as private as possible. But no - you can't just select option #2.  If you want to select either #1 or #2, you MUST opt in to #3 - turning on ALL connected experiences.  \n\nSo, If your **main goal is privacy**, you'd have to disable everything and manually back up.  \n\nHOW HAS MICROSOFT BEEN ALLOWED TO GET AWAY WITH THIS???  How is this legal?  WTAF is going on???\n\n\n\n", "author": "voubar", "created_time": "2025-02-06T15:53:07", "url": "https://reddit.com/r/microsoft/comments/1ij5pj4/updated_privacy_policy_should_be_illegal/", "upvotes": 12, "comments_count": 23, "sentiment": "neutral", "engagement_score": 58.0, "source_subreddit": "microsoft", "hashtags": null, "ticker": "MSFT"}, {"platform": "reddit", "post_id": "reddit_1ij8cgh", "title": "Any tips or insight on Business Development Manager roles?", "content": "Hi all, I recently scheduled an intro phone call with a hiring manager at Microsoft to discuss this role. Could anyone with experience provide me with some insight on it? I come from 4 years of sales, is this just another BDR role? \n\nTIA", "author": "pbandit11", "created_time": "2025-02-06T17:39:42", "url": "https://reddit.com/r/microsoft/comments/1ij8cgh/any_tips_or_insight_on_business_development/", "upvotes": 0, "comments_count": 1, "sentiment": "neutral", "engagement_score": 2.0, "source_subreddit": "microsoft", "hashtags": null, "ticker": "MSFT"}, {"platform": "reddit", "post_id": "reddit_1ijb1m0", "title": "Microsoft’s AI boss just raided Google. He poached two scientists who built a tool that can transform ho-hum text into a riveting podcast", "content": "", "author": "ControlCAD", "created_time": "2025-02-06T19:29:03", "url": "https://reddit.com/r/microsoft/comments/1ijb1m0/microsofts_ai_boss_just_raided_google_he_poached/", "upvotes": 95, "comments_count": 8, "sentiment": "neutral", "engagement_score": 111.0, "source_subreddit": "microsoft", "hashtags": null, "ticker": "MSFT"}, {"platform": "reddit", "post_id": "reddit_1ijfbj7", "title": "Investors Who Shunned the U.S. Office Market Are Coming Back", "content": "The volume of office building sales increased to $63.6 billion in 2024, up 20% from 2023, according to data firm MSCI. That activity still pales compared with 2015 to 2019, when volume averaged $142.9 billion a year. But it marked the first increase since 2021.", "author": "PrestigiousCat969", "created_time": "2025-02-06T22:23:29", "url": "https://reddit.com/r/finance/comments/1ijfbj7/investors_who_shunned_the_us_office_market_are/", "upvotes": 60, "comments_count": 21, "sentiment": "neutral", "engagement_score": 102.0, "source_subreddit": "finance", "hashtags": null, "ticker": "MSFT"}, {"platform": "reddit", "post_id": "reddit_1ijg7xa", "title": "MicrosoftDocs GitHub Issues Hidden", "content": "", "author": "myroon5", "created_time": "2025-02-06T23:02:23", "url": "https://reddit.com/r/microsoft/comments/1ijg7xa/microsoftdocs_github_issues_hidden/", "upvotes": 0, "comments_count": 2, "sentiment": "neutral", "engagement_score": 4.0, "source_subreddit": "microsoft", "hashtags": null, "ticker": "MSFT"}]