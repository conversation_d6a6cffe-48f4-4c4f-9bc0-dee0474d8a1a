[{"platform": "reddit", "post_id": "reddit_1kl9tu2", "title": "Using long puts and margin as an inflation protection stratagy.", "content": "So I have a stratagy idea and I don't know much about the bear thesis for it and would like help evaluating more of the downsides to this idea.\n\nWhile looking though leaps I saw that the premium for a dec 2027 $150 put was $45\nBecause I have margin and the margin requirment for nvda is 30% i only need $5000 to cover the collateral and would receive $4500 in premium.\nThe USD being the reserve currency and trhough deficit spending inflation will always be present the value of stocks will stay the same but the amount of usd required to purchase them will go up.  So I figured inflation was on my side with this stratagy idea.\nBy selling the $150 put for $45, that would put my cost basis for nvda at $105/share (it does not need to be nvda just what I'm using for this idea) to buy out the put when nvda is below it's current price of $122 would inccur a loss but if assigned at or above nvda $106 would result in a net gain minus the lost opportunity cost of time.  To my understanding, this would work as essentially an interest-free loan for 3 years with which to buy additional shares. This locks in the ability to buy NVDA at $105/share for the next three years as money i save from work to invest and interest i would earn off the premium in either bonds or a HYSA would go towards paying off the inevitable margin loan when I get assigned.  To my understanding, doing this with in individual stock/stocks is rather risky but seems less risky than long calls.  The s&p500 has returned on average 8% per year so if I chose a strike that is 26% out (8% compounded 3 years) that would be in line with conventional returns from the index, by doing this i am protecting future earnings from inflation while continuing to earn interest off of the initial premium collected.  Now this definitely seems far too good to be true and I would like help evaluating the bear thesis/why this won't work.  As well if a name for such a stratagy exists.", "author": "Embarrassed_<PERSON>rian17", "created_time": "2025-05-13T01:31:04", "url": "https://reddit.com/r/options/comments/1kl9tu2/using_long_puts_and_margin_as_an_inflation/", "upvotes": 4, "comments_count": 10, "sentiment": "bullish", "engagement_score": 24.0, "source_subreddit": "options", "hashtags": null, "ticker": "NVDA"}, {"platform": "reddit", "post_id": "reddit_1kler1z", "title": "MVST released a profitable quarter, here is what you need to know", "content": "Okay degenerates, while everyone’s either chasing AI stocks into the stratosphere or crying over their -85% SPAC bags, Microvast ($MVST) — yes, the one you forgot existed — just quietly posted a profitable Q1 and keeps guiding up.\n\nHere’s the TLDR for those allergic to reading: Revenue: $116.5M (up 43% YoY) Gross margin: 36.9% (up from 21.2% — not a typo) Net income (GAAP): $61.8M (last year was -$24.8M, so…that’s a glow-up) Adj. EBITDA: $28.5M vs. -$3.7M last year Cash: $123M on hand 2025 revenue guide: $450M–$475M with 30% gross margin target\n\nSo… yeah, they’re not bleeding cash anymore. And they’re scaling. The Huzhou plant is ramping. They’re pushing high-energy battery packs. Not hype — actual deliveries, actual factories, and actual balance sheet improvements.\n\n⸻\n\nWhy I think it rips: Undervalued AF: Trading around $2.70 with fundamentals improving. Market still treats this like a zombie SPAC. EV macro tailwinds: Everyone’s bearish on EVs right now, which is exactly when bottom-feeders like MVST get ignored. Short-term squeeze potential: Low float, improved numbers — enough to light a fuse if sentiment flips. No insane dilution (yet): They raised last year, but haven’t gone full clown mode. Still holding decent cash.\n\n⸻\n\nRisks (because we’re not totally braindead): China exposure. Macro stuff could smack them. Still not profitable every quarter — this one included some fair value accounting gains. The market could ignore this for another 6 months while chasing GPU dreams. Retail doesn’t care about earnings anymore unless there’s a rocket emoji involved.", "author": "Over-Tea2419", "created_time": "2025-05-13T06:06:40", "url": "https://reddit.com/r/pennystocks/comments/1kler1z/mvst_released_a_profitable_quarter_here_is_what/", "upvotes": 80, "comments_count": 26, "sentiment": "bullish", "engagement_score": 132.0, "source_subreddit": "pennystocks", "hashtags": null, "ticker": "NVDA"}, {"platform": "reddit", "post_id": "reddit_1klpqp7", "title": "Big buys in the June 2027 Contracts", "content": "Saw quite some activity yesterday in the June 2027 contracts.\n\nfirst off was $NVDA with an opening sale of 6770 June 2027 $85 puts @ 14.81 to $14.71\n\nthen we had $AMZN with a massive buy go 2575 June 2027 $180 calls for $43.65 for $11M\n\n$GOOGL 2750 June 2027 $155 Calls bought up to $26.90 for $10M\n\n$ARM 2400 June 2027 $105 calls bought for $41.50\n\nand today we had\n\n$ARM another 2150 June 2027 $125 calls bought\n\n$ADI with 2076 June 2027 $220 Calls bought for $10M\n\n$C with 7360 June 2027 $75 calls bought for $10M\n\n$GOOGL 2990 June 2027 $160 calls for $33.45\n\n$GILD  4,762 June 2027 $97.50 calls $21 looking to add to OI as well\n\n$VRTX 979 of the June 2027 $440 calls for $10M\n\n$MA with $10M buy of the June 2027 $570 calls today\n\n$AVGO getting some buys as well\n\n$TSLL sees 1000 January 2027 $14.70 bull synthetics at $0.65 credit. Also 2300 June 27th $13 calls buy at $2\n\n\nthis is as of 12:03.  I may have missed a few but damn\n\n\nedit 5/14: KKR with 2857 June 2027 $125 calls bought, another $10M buy in the June 2027s\n\n$ARM 2000 June 2027 $130 calls buy for $10M, they keep reaching for higher strikes but keeping original positions\n\n5/14 10:02am: $SCHW June 2027 $85 calls bought, 2700 at 18.90 adding to OI\n\n5/15 9:54 $CRM $10M June 2027 call buy with 1487 of the $290 calls bought\n\n$LLY 825 June 2027 $830 calls bought\n\n5/16 12:00 $ARM seeing yet another buy of 2000 June 2027 $130 calls today at $50.25, adding to OI check, incredible accumulation continues in 2027 expy", "author": "Gfnk0311", "created_time": "2025-05-13T16:03:16", "url": "https://reddit.com/r/options/comments/1klpqp7/big_buys_in_the_june_2027_contracts/", "upvotes": 40, "comments_count": 9, "sentiment": "bullish", "engagement_score": 58.0, "source_subreddit": "options", "hashtags": null, "ticker": "NVDA"}, {"platform": "reddit", "post_id": "reddit_1klq24v", "title": "Inconsistency in theory for parallel binomial (American) option pricing?", "content": "Not sure if this is the correct place to ask, but here it goes. I am writing about GPU-accelerated option pricing algorithms for a Bachelor's thesis, and have found this paper:\n\n[https://www.ccrc.wustl.edu/\\~roger/papers/gcb09.pdf](https://www.ccrc.wustl.edu/%7Eroger/papers/gcb09.pdf)\n\nI do understand the outline of this algorithm for European-style options, where no early-exercise is possible. But for American-style options where this is a possibility, the standard sequential binomial model calculates the value of the option at the current node as a maximum of either the discounted continuation value of holding it to the next period (so just like for a European option) or the value of exercising it immediately on the spot (i.e. the difference of the current asset price and the specified strike price).\n\nThis algorithm uses a recursive formula to establish relative option prices between nodes over several time-steps. This is then utilized by splitting the entire lattice into partitions, calculating relative option prices between every partition boundary, and finally, propagating the option values over these partitions from the terminal nodes back to the initial node. This allows us to skip many intermediate calculations.\n\nThe paper then states that \"Now, the option prices could be propagated from one boundary to the next, starting from the last with the dependency relation just established, with a stride of T /p time steps until we reach the first partition, which bears the option price at the current moment, thus achieving a speed-up of p, as shown in figure (3). Now, with the knowledge of the option prices at each boundary, the values in the interior nodes could be filled in parallel for all the partitions, if needed(as in American options).\"\n\nI feel like this is quite vague, and I don't really get how to modify this to work with American options. I feel like the main recursive equation must be changed to incorporate the early-exercise possibility at every step, and I am not convinced that we have such a simple equation for relating option prices across several time steps like before.\n\nCould someone explain the gaps in my knowledge here, or shed some light on how exactly you tailor this to work for American options?\n\nThanks!", "author": "Daniel01m", "created_time": "2025-05-13T16:15:43", "url": "https://reddit.com/r/options/comments/1klq24v/inconsistency_in_theory_for_parallel_binomial/", "upvotes": 5, "comments_count": 3, "sentiment": "bearish", "engagement_score": 11.0, "source_subreddit": "options", "hashtags": null, "ticker": "NVDA"}, {"platform": "reddit", "post_id": "reddit_1klu2cx", "title": "US Weighs Letting UAE Buy Over a Million Advanced Nvidia Chips, Bloomberg News Reports", "content": "No paywall: [https://money.usnews.com/investing/news/articles/2025-05-13/us-weighs-letting-uae-buy-over-a-million-advanced-nvidia-chips-bloomberg-news-reports](https://money.usnews.com/investing/news/articles/2025-05-13/us-weighs-letting-uae-buy-over-a-million-advanced-nvidia-chips-bloomberg-news-reports)\n\n(Reuters) - The <PERSON> administration is weighing a deal that would allow the UAE to import more than a million advanced Nvidia chips, a quantity that far exceeds limits under Biden-era AI chip regulations, Bloomberg News reported on Tuesday.\n\nThe deal, which is still being negotiated and could change, would let the UAE import 500,000 of the most advanced chips on the market each year from now to 2027, the report said, citing people familiar with the matter.\n\nWhile one-fifth would be set aside for the Abu Dhabi AI firm G42, the rest will go to U.S. companies building data centers in the Gulf nation, according to the report.\n\nChatGPT-maker OpenAI, which may announce new data-center capacity in the UAE as soon as this week, could be one of those companies, the report said.\n\nThe report comes on the heels of U.S. President <PERSON> securing a $600 billion commitment from Saudi Arabia to invest in the United States.\n\nThe Department of Commerce and OpenAI did not immediately respond to Reuters' requests for comment, while Nvidia declined to comment.\n\nAbu Dhabi's artificial intelligence push has mostly been led by state-backed G42, which has drawn increased scrutiny from China hawks in Washington amid fears that the UAE is becoming a conduit for China to receive advanced American AI technology it is blocked from getting directly from the U.S.\n\nAccording to the Bloomberg report, G42 could purchase computing capabilities equivalent to between 1 million and 1.5 million H100 chips over the lifetime of the deal. That is around four times more than it would have been allowed to buy under a Biden-era chip export control framework, known as AI diffusion, it said.\n\nTrump's administration plans to rescind and modify this rule, which curbed the export of sophisticated AI chips, a spokeswoman for the Department of Commerce had said last week.", "author": "callsonreddit", "created_time": "2025-05-13T18:51:30", "url": "https://reddit.com/r/StockMarket/comments/1klu2cx/us_weighs_letting_uae_buy_over_a_million_advanced/", "upvotes": 65, "comments_count": 24, "sentiment": "bullish", "engagement_score": 113.0, "source_subreddit": "StockMarket", "hashtags": null, "ticker": "NVDA"}, {"platform": "reddit", "post_id": "reddit_1km0tvb", "title": "NVDA vs NVDL", "content": "Can’t go wrong with either one because I want to pick up some calls expiring 6/20. However, is it better to go NVDL here for more gains? And with what strike?", "author": "giamann88", "created_time": "2025-05-13T23:33:29", "url": "https://reddit.com/r/options/comments/1km0tvb/nvda_vs_nvdl/", "upvotes": 2, "comments_count": 2, "sentiment": "bullish", "engagement_score": 6.0, "source_subreddit": "options", "hashtags": null, "ticker": "NVDA"}]