[{"platform": "reddit", "post_id": "reddit_1kf1vc6", "title": "Should I downgrade to Windows 10?", "content": "Exactly what it says on the tin. I've been hearing some talk about people downgrading but I'm not really sure why. Is the UI much better, or are there some performance increases I'm unaware of? I'm leaning towards doing it just out of peer pressure, but I don't really understand the benefits either way. I know support ends soon, but people seem to like it anyway, and I guess I just want to get the hype.", "author": "LogOffShell", "created_time": "2025-05-05T03:17:08", "url": "https://reddit.com/r/microsoft/comments/1kf1vc6/should_i_downgrade_to_windows_10/", "upvotes": 0, "comments_count": 11, "sentiment": "neutral", "engagement_score": 22.0, "source_subreddit": "microsoft", "hashtags": null, "ticker": "MSFT"}, {"platform": "reddit", "post_id": "reddit_1kf2pdc", "title": "Microsoft Background Check", "content": "I received an offer from Microsoft US but I applied with my old resume where I didn’t update my current job which I’ve been working for past 3 months. When I applied it’s only been a month and I didn’t want to put that on my resume yet. Should I tell recruiter about it now and get my resume updated in their system or can I just add this employee while filling out background verification? Would that cause any issues because it’s not on my resume? I prepared so hard to land this offer, I’m afraid if background check might jeopardize my offer. Highly appreciate any inputs.", "author": "marketmanipulator69", "created_time": "2025-05-05T04:04:10", "url": "https://reddit.com/r/microsoft/comments/1kf2pdc/microsoft_background_check/", "upvotes": 3, "comments_count": 11, "sentiment": "bearish", "engagement_score": 25.0, "source_subreddit": "microsoft", "hashtags": null, "ticker": "MSFT"}, {"platform": "reddit", "post_id": "reddit_1kfcboo", "title": "Hims &amp; <PERSON><PERSON> Appoints Global Operations Expert and Amazon Veteran as Chief Operations Officer", "content": "", "author": "AmazonNewsBot", "created_time": "2025-05-05T14:00:04", "url": "https://reddit.com/r/amazon/comments/1kfcboo/hims_amp_hers_appoints_global_operations_expert/", "upvotes": 1, "comments_count": 0, "sentiment": "neutral", "engagement_score": 1.0, "source_subreddit": "amazon", "hashtags": null, "ticker": "MSFT"}, {"platform": "reddit", "post_id": "reddit_1kffjnz", "title": "Microsoft careers profile question", "content": "On Microsoft's careers page, applicants can upload up to 10 documents to their profile when applying for a position. If an applicant deletes those documents after uploading them, do Microsoft recruiters still have access to them? Or are they completely removed from the recruiter’s view?", "author": "JasonF818", "created_time": "2025-05-05T16:12:05", "url": "https://reddit.com/r/microsoft/comments/1kffjnz/microsoft_careers_profile_question/", "upvotes": 0, "comments_count": 1, "sentiment": "neutral", "engagement_score": 2.0, "source_subreddit": "microsoft", "hashtags": null, "ticker": "MSFT"}, {"platform": "reddit", "post_id": "reddit_1kfidqg", "title": "Why does Microsoft get bored with app names and apps in general?", "content": "People say Microsoft isn't dynamic but their rebranding is more dynamic than that of Apple lol. Can anyone explain why they change names or completely remove/revamp apps and can't seem to be satisfied with developing something for more than 10 years without a name change or deprecation?\n\nNetMeeting / MSN / Windows Messenger -> Live Messenger -> Skype -> Teams\n\nMicrosoft Internet Mail and News -> Outlook Express -> Mail (Vista) -> Windows Live Mail -> Mail (10/11) -> Outlook\n\nMeanwhile Apple has had an app called Mail since 2003. No name change for more than 20 years! (even if the functionality/looks probably changed a lot, I don't follow it).\n\nYou never know with Microsoft, they're closing Skype. 10 years down the line they might rename Teams to something else, like WinMeet ;)\n\nWhat's behind the constant need to reinvent the wheel?", "author": "deleted", "created_time": "2025-05-05T18:03:50", "url": "https://reddit.com/r/microsoft/comments/1kfidqg/why_does_microsoft_get_bored_with_app_names_and/", "upvotes": 21, "comments_count": 44, "sentiment": "neutral", "engagement_score": 109.0, "source_subreddit": "microsoft", "hashtags": null, "ticker": "MSFT"}, {"platform": "reddit", "post_id": "reddit_1kfpyit", "title": "Gears of War: Reloaded is a remaster of Delta Squad's first adventure, coming to Xbox and PlayStation", "content": "Gears of War: Reloaded is a new remaster of Delta Squad's original adventure, and it's coming to PlayStation alongside Xbox and PC.", "author": "ControlCAD", "created_time": "2025-05-05T23:16:06", "url": "https://reddit.com/r/microsoft/comments/1kfpyit/gears_of_war_reloaded_is_a_remaster_of_delta/", "upvotes": 5, "comments_count": 1, "sentiment": "bullish", "engagement_score": 7.0, "source_subreddit": "microsoft", "hashtags": null, "ticker": "MSFT"}, {"platform": "reddit", "post_id": "reddit_1kfqpen", "title": "Exam", "content": "I will take the MS 102 Microsoft 365 Administrator exam in 1.5 months. What do you recommend?", "author": "NorthWind3411", "created_time": "2025-05-05T23:51:22", "url": "https://reddit.com/r/microsoft/comments/1kfqpen/exam/", "upvotes": 0, "comments_count": 3, "sentiment": "neutral", "engagement_score": 6.0, "source_subreddit": "microsoft", "hashtags": null, "ticker": "MSFT"}]