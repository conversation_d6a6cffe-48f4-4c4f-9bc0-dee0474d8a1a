[{"platform": "reddit", "post_id": "reddit_1i7565v", "title": "Quality of Microsoft Designer", "content": "Who at Microsoft is responsible for product quality standards? The generated images in Microsoft Designer unfortunately remain unusable and there has been no improvement in the last two months.\n\nFor example, the PR16 is unable to draw an even line, let alone handle text. Lighting is also handled incorrectly: in some scenes, characters' eyes are barely visible, and the overall colour contrast causes visual discomfort. The generation of objects, such as the moon, leaves much to be desired.\n\nNevertheless, the home page continues to showcase the quality of the images, which appear to be only available on the PR13 model. I would like to know who is responsible for quality control of this product, and are there any fixes or updates planned in the near future?", "author": "MINIVV", "created_time": "2025-01-22T06:43:32", "url": "https://reddit.com/r/microsoft/comments/1i7565v/quality_of_microsoft_designer/", "upvotes": 1, "comments_count": 0, "sentiment": "bullish", "engagement_score": 1.0, "source_subreddit": "microsoft", "hashtags": null, "ticker": "MSFT"}, {"platform": "reddit", "post_id": "reddit_1i77ge6", "title": "Custom hotkey/keyboard shortcut that returns a text string", "content": "I'm making this post for anyone in the future looking for a solution to this feature. If you already know about the solution please ignore the following but it took me days of research to finally find it with a lot of forums leading to convoluted dead ends.\n\n\nIn short if you want to set up a keyboard shortcut combination that inserts a predetermined string of characters (eg. \"@gmail.com\") you should install from the Microsoft Store \"Microsoft PowerToys\" & set up the combinations in the Keyboard Manager>Remap a shortcut.\n", "author": "Boflator", "created_time": "2025-01-22T09:35:05", "url": "https://reddit.com/r/microsoft/comments/1i77ge6/custom_hotkeykeyboard_shortcut_that_returns_a/", "upvotes": 2, "comments_count": 2, "sentiment": "bearish", "engagement_score": 6.0, "source_subreddit": "microsoft", "hashtags": null, "ticker": "MSFT"}, {"platform": "reddit", "post_id": "reddit_1i77mh5", "title": "I swear, MS Word is some kind of eldritch rage-energy feeding device.", "content": "Trying to accomplish the simplest of tasks (in this instance, separate page numbering from front matter to the actual body of work) yields anguish, despair, and little else. \n\nI've been poring over no less than five separate help articles for the past three hours. As frustration grows a solution seemingly presents itself only to yield more issues. \n\nI FINALLY was able to start numbering the main body with separate numbers only to have the \"Chapter One\" 1st page be labeled as \"3.\" Wat. \n\nSo I spent 45 minutes trying to find the appropriate place to insert the \"Section Break\" and eventually got the Chapter One 1st page to be labeled \"one\"...but now page two is \"3\"!!!!! From then on it continues normally. \n\nTell me that Microsoft DOESN'T have some kind of deal worked out with book formatting peeps as they've made this thing so incredibly fucking frustrating to use - and I play like, REALLY intricate games. Well, Rimworld. and Factorio. and Terra Invicta. Semi-complicated. ", "author": "DescriptionOwn6184", "created_time": "2025-01-22T09:48:08", "url": "https://reddit.com/r/microsoft/comments/1i77mh5/i_swear_ms_word_is_some_kind_of_eldritch/", "upvotes": 6, "comments_count": 7, "sentiment": "neutral", "engagement_score": 20.0, "source_subreddit": "microsoft", "hashtags": null, "ticker": "MSFT"}, {"platform": "reddit", "post_id": "reddit_1i7991o", "title": "Rewrite windows", "content": "From win 7 and forward Windows has been more and more \"fat\", eats more ram, uses more cpu...you guys need to rethink the Windows code...build an ai to help you 👌. I have several computers and some of them is on older hardware..if i put any distro of Linux on them they are snappy again. But i need Windows. I believe that there are billions of older computers that would need a new version of windows that is secure and up to date, that would work on older hardware.", "author": "kallstrom_74", "created_time": "2025-01-22T11:43:24", "url": "https://reddit.com/r/microsoft/comments/1i7991o/rewrite_windows/", "upvotes": 0, "comments_count": 7, "sentiment": "neutral", "engagement_score": 14.0, "source_subreddit": "microsoft", "hashtags": null, "ticker": "MSFT"}, {"platform": "reddit", "post_id": "reddit_1i79cvv", "title": "Remove Copilot nonsense", "content": "Hi,\n\nI was trying to quickly respond to an email and get on with my day. Unfortunately, it seems to be impossible to do so without MS jamming Copilot nonsense into my face at the start of every line.\n\nStop shoving new \"features\" down my throat, please, and simply add an obvious way to disable new features. I have zero interest in AI, and would rather cancel my Office 365 (or is it Copilot, now?) membership than continue to pay for the constant inconvenience of having half-baked Copilot junk shoved in my face when trying to send a single-paragraph email to a family member.\n\nI guess the fact that I'm unable to include an image displaying the issue is a testament to how beloved MS is over here?\n\nGoing into system settings and unticking something under the taskbar settings, as I saw mentioned somewhere, didn't do it. Has anyone been able to disable these Copilot annoyances, particularly for Outlook/Office? And am I the only one annoyed by the way it's jammed into software that worked fine for decades without it?", "author": "straef", "created_time": "2025-01-22T11:50:11", "url": "https://reddit.com/r/microsoft/comments/1i79cvv/remove_copilot_nonsense/", "upvotes": 8, "comments_count": 18, "sentiment": "neutral", "engagement_score": 44.0, "source_subreddit": "microsoft", "hashtags": null, "ticker": "MSFT"}, {"platform": "reddit", "post_id": "reddit_1i7cmm6", "title": "Software Engineer II - AI/ML", "content": "Hi,\nI’ve just received an email with a Codility coding assignment (1 question - 40 minutes) for the role above. It has no expiration date, and it’s been a while since I did leetcode. For Microsoft, which patterns should I focus on preparing? And how much time should I give myself at minimum?", "author": "Adventurous-Fee3087", "created_time": "2025-01-22T14:43:13", "url": "https://reddit.com/r/microsoft/comments/1i7cmm6/software_engineer_ii_aiml/", "upvotes": 3, "comments_count": 9, "sentiment": "neutral", "engagement_score": 21.0, "source_subreddit": "microsoft", "hashtags": null, "ticker": "MSFT"}, {"platform": "reddit", "post_id": "reddit_1i7gsxt", "title": "M365: I do not understand anything", "content": "Hi, \n\nI am trying to buy a M365 and, unfortunately, I do not understand anything from the limitless pages with plans and FAQs. \n\nEnterprise is too much for me, business standard or personal seem alright. \n\nI’ve talked to two Microsoft agents, the first conversation being abysmal (no direct responses, bad copilot replies; just horrendous). \n\nA. General\n\nOne of the agents told me only certain Enterprise accounts can opt out of training the model (through EDP—Enterprise Data Protection).\n\nA.1. Is that information correct? \n\nA.1.1. If not, what other plans would allow me to do that?\n\nIf this is correct, I would be forced to buy a Google subscription. \n\nB. M365 Personal vs. M365 Business Standard \n\nB.1. Designer is only available on M365 Personal?\n\nB.2. What are the Copilot differences between the two plans? \n\nB.3. Am I protected against a phishing, ransomware and other cyber threats in any of these 2 plans? \n\nB.4. Do I get a custom e-mail address in any of the plans? \n\nB.5. Are my messages and work encrypted in any of these plans? \n\nB.6. If I buy M364 Business Standard WITHOUT Teams, are there any other differences?\n\nC. Copilot Features in Personal and Business Plans vs. separate license for M365 Copilot ($30)\n\nC.1. What are the difference between Copilot M365 Personal and Copilot M365 Business Standard? \n\nC.2. What are the differences between Copilot M364 Personal and M365 Copilot ($30)?\n\nC.3. What are the differences between Copilot M364 Business Standard and M365 Copilot ($30)?\n\nD. Other questions\n\nD.1. Am I able to opt out from the Copilot preinstalled in Windows? I do not have a M365 account, but I have a W11 Pro license. \n\nI really don’t understand anything. \n\nThank you! ", "author": "xYoKx", "created_time": "2025-01-22T17:37:54", "url": "https://reddit.com/r/microsoft/comments/1i7gsxt/m365_i_do_not_understand_anything/", "upvotes": 0, "comments_count": 22, "sentiment": "bullish", "engagement_score": 44.0, "source_subreddit": "microsoft", "hashtags": null, "ticker": "MSFT"}, {"platform": "reddit", "post_id": "reddit_1i7ieir", "title": "Microsoft will automatically keep you signed in to your account starting in February", "content": "", "author": "BippityBoppityWhoops", "created_time": "2025-01-22T18:42:06", "url": "https://reddit.com/r/microsoft/comments/1i7ieir/microsoft_will_automatically_keep_you_signed_in/", "upvotes": 171, "comments_count": 20, "sentiment": "neutral", "engagement_score": 211.0, "source_subreddit": "microsoft", "hashtags": null, "ticker": "MSFT"}, {"platform": "reddit", "post_id": "reddit_1i7jrek", "title": "Microsoft 365 Personal goes up in price by $30", "content": "Just received this email...\n\n*Thank you for being a valued Microsoft 365 subscriber. To reflect the value we’ve added over the past decade, address rising costs, and enable us to continue delivering new innovations, we’re increasing the price of your subscription.*\n\n*Effective February 14, 2025, the price for* ***Microsoft 365 Personal*** *subscriptions will increase from* ***USD 69.99******^(\\*)*** ***per year*** *to* ***USD 99.99******^(\\*)*** ***per year****.* *To continue with the new price, no action is needed—your payment method on file will be automatically charged. To make changes to your subscription plan or turn off recurring billing, visit your* [*Microsoft account*](https://t2.infomails.microsoft.com/r/?id=hec627d5,********,********) *at least two days before your next billing date.*\n\n*By maintaining your subscription, you’ll enjoy secure cloud storage, advanced security for your data and devices, and cutting-edge AI-powered features, along with all your other* [*subscription benefits*](https://t2.infomails.microsoft.com/r/?id=hec627d5,********,********)*. Thank you for choosing Microsoft.*\n\n*Learn more about how to manage your subscription, including* [*how to cancel*](https://t2.infomails.microsoft.com/r/?id=hec627d5,********,********) *and* [*switch your subscription*](https://t2.infomails.microsoft.com/r/?id=hec627d5,********,********)*.*", "author": "Perry7609", "created_time": "2025-01-22T19:35:30", "url": "https://reddit.com/r/microsoft/comments/1i7jrek/microsoft_365_personal_goes_up_in_price_by_30/", "upvotes": 26, "comments_count": 22, "sentiment": "bullish", "engagement_score": 70.0, "source_subreddit": "microsoft", "hashtags": null, "ticker": "MSFT"}]