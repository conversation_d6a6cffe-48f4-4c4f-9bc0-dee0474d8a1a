[{"platform": "reddit", "post_id": "reddit_1il3b6i", "title": "Microsoft is doing a lot of stuff right, but still need to better itself", "content": "For the past two decades, I've seen a lot of change from Microsoft. Mostly good change.\n\nBut obviously there's still some ironing to do.\n\n[https://www.reddit.com/r/browsers/comments/1ikj3xt/microsoft\\_support\\_page\\_lies\\_about\\_how\\_to/](https://www.reddit.com/r/browsers/comments/1ikj3xt/microsoft_support_page_lies_about_how_to/)\n\nFound this hard to believe, so I tried it myself.\n\nWent to Google and searched:\n\n\\- how to uninstall google chome  \n\\- how to uninstall firefox  \n\\- how to uninstall brave  \n\\- how to uninstall microsoft edge\n\nThe result from the official brand took me to:  \n[https://support.google.com/chrome/answer/95319?hl=en&co=GENIE.Platform%3DDesktop](https://support.google.com/chrome/answer/95319?hl=en&co=GENIE.Platform%3DDesktop)  \n[https://support.mozilla.org/en-US/kb/uninstall-firefox-from-your-computer#](https://support.mozilla.org/en-US/kb/uninstall-firefox-from-your-computer#)  \n[https://support.brave.com/hc/en-us/articles/4404876135565-How-do-I-uninstall-Brave](https://support.brave.com/hc/en-us/articles/4404876135565-How-do-I-uninstall-Brave)  \n[https://www.microsoft.com/en-us/edge/?form=MT00OR&cs=*********&ch=1](https://www.microsoft.com/en-us/edge/?form=MT00OR&cs=*********&ch=1)\n\nThis is a very sad move from Microsoft and something I thought the company was finally being able to turn away from.\n\n**EDIT:**\n\nI didn't explain myself properly on the first attempt.\n\nIf you Google for it, you'll get an official result from Microsoft:\n\nThe page is Titled: Uninstall Microsoft Edge\n\n[https://www.microsoft.com/en-us/edge/uninstall-edge](https://www.microsoft.com/en-us/edge/uninstall-edge)\n\nBut, if you visit such page as a client, you're forwarded to another:\n\n[https://www.microsoft.com/en-us/edge/?ch=1&cs=4112006293&form=MA13FJ](https://www.microsoft.com/en-us/edge/?ch=1&cs=4112006293&form=MA13FJ)\n\n**My issue is with Microsoft blatantly messing with the search results!**\n\nI've added an image with the result from Google search:\n\n[https://imgur.com/a/16Xkxcc](https://imgur.com/a/16Xkxcc)", "author": "frankielc", "created_time": "2025-02-09T01:29:06", "url": "https://reddit.com/r/microsoft/comments/1il3b6i/microsoft_is_doing_a_lot_of_stuff_right_but_still/", "upvotes": 6, "comments_count": 24, "sentiment": "neutral", "engagement_score": 54.0, "source_subreddit": "microsoft", "hashtags": null, "ticker": "MSFT"}, {"platform": "reddit", "post_id": "reddit_1il5rcg", "title": "Microsoft Identity and Access Administrator SC-300 Exam", "content": "Can anyone recommend good study material I'm thinking of buying\nhttps://a.co/d/16gi1Md\n\n<PERSON> and 2 more\nMicrosoft Identity and Access Administrator SC-300 Exam Guide: Gain the confidence to pass the SC-300 exam using exam-focused study resources\nISBN-13: 978-1836200390, ISBN-10: 1836200390", "author": "Bulky_Novel_4224", "created_time": "2025-02-09T03:38:53", "url": "https://reddit.com/r/microsoft/comments/1il5rcg/microsoft_identity_and_access_administrator_sc300/", "upvotes": 2, "comments_count": 6, "sentiment": "bullish", "engagement_score": 14.0, "source_subreddit": "microsoft", "hashtags": null, "ticker": "MSFT"}, {"platform": "reddit", "post_id": "reddit_1ildztw", "title": "Migrating imap to exchange", "content": "Hello, \n\nCould someone explain to me how exchange works? \n\nWe have a rented email server with @domain.com(imap and web access). We want to migrate to exchange on office 365 bussines subscription. If we migrate to exchange via imap migration (providing email and password) will emails stay on imap server? \n\n", "author": "blindpd", "created_time": "2025-02-09T12:48:32", "url": "https://reddit.com/r/microsoft/comments/1ildztw/migrating_imap_to_exchange/", "upvotes": 1, "comments_count": 5, "sentiment": "neutral", "engagement_score": 11.0, "source_subreddit": "microsoft", "hashtags": null, "ticker": "MSFT"}, {"platform": "reddit", "post_id": "reddit_1ils4r0", "title": "What is your tried and true implementation of Microsoft app?", "content": "I'm and looking for ways to automate productivity and make life simpler.", "author": "LivinJH", "created_time": "2025-02-09T23:14:47", "url": "https://reddit.com/r/microsoft/comments/1ils4r0/what_is_your_tried_and_true_implementation_of/", "upvotes": 2, "comments_count": 9, "sentiment": "neutral", "engagement_score": 20.0, "source_subreddit": "microsoft", "hashtags": null, "ticker": "MSFT"}]