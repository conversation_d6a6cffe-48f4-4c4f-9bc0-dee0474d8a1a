[{"platform": "reddit", "post_id": "reddit_1kjw1s9", "title": "[SOLVED] BSOD at 77% - fltmgr.sys Error During Windows To Go Install (WinToUSB / EasyUEFI)", "content": "Problem:\nYou try to install Windows 11 to an SSD using WinToUSB or EasyUEFI, but during the first boot or setup, it crashes at exactly 77% with a fltmgr.sys BSOD (often with INACCESSIBLE_BOOT_DEVICE).\n\n⸻\n\nCause:\n\nThis happens when:\n\t•\tYou use VHD/VHDX mode (virtual disk) instead of native install.\n\t•\tThe bootloader isn’t properly installed or recognized by UEFI.\n\t•\tYour system runs in Legacy BIOS or Secure Boot is enabled, causing driver issues.\n\n⸻\n\nSolution (WORKS 100%)\n\t1.\tUse WinToUSB (from EasyUEFI)\n\t•\tDownload: https://www.easyuefi.com/wintousb\n\t2.\tCreate a Native Windows To Go Install on Your SSD\n\t•\tSelect your Windows ISO (11 or 10)\n\t•\tSet Partition Scheme: GPT for UEFI\n\t•\tSet Installation Mode: Legacy (not VHD or VHDX)\n\t•\tTarget: Your internal SSD\n\t3.\tBIOS Settings Before First Boot\n\t•\tSet Boot Mode to UEFI\n\t•\tDisable Secure Boot\n\t•\tMake SSD the first boot device\n\t4.\tBoot & Enjoy\n\t•\tNo more 77% crash\n\t•\tNo fltmgr.sys issues\n\t•\tFully working Windows install on SSD via USB\n\n⸻\n\nTested and Confirmed By:\n\t•\t<PERSON><PERSON> (real user who fought and won the 77% BSOD war)\n\t•\tWith help from ChatGPT troubleshooting sessions\n", "author": "DoomsFyter1", "created_time": "2025-05-11T08:15:16", "url": "https://reddit.com/r/microsoft/comments/1kjw1s9/solved_bsod_at_77_fltmgrsys_error_during_windows/", "upvotes": 26, "comments_count": 5, "sentiment": "bearish", "engagement_score": 36.0, "source_subreddit": "microsoft", "hashtags": null, "ticker": "MSFT"}, {"platform": "reddit", "post_id": "reddit_1kjwpfh", "title": "Not Responding - Reopen Application choice", "content": "You may came across when using an application on Windows, is not responding. Most of the time if you still needed the application, you waited until it respondes again, or close and open the application again. Well last night i updated my Windows 11 laptop, and today when <PERSON><PERSON> wasnt responding, i saw 3 options instead of the normal 2. \"Reopen Application\" (or similar). And legit it reponed Paint. Cool feature!", "author": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "created_time": "2025-05-11T09:02:16", "url": "https://reddit.com/r/microsoft/comments/1kjwpfh/not_responding_reopen_application_choice/", "upvotes": 1, "comments_count": 0, "sentiment": "neutral", "engagement_score": 1.0, "source_subreddit": "microsoft", "hashtags": null, "ticker": "MSFT"}, {"platform": "reddit", "post_id": "reddit_1kjxds4", "title": "microsoft store servers", "content": "What’s going on with Microsoft Store servers? They’re incredibly slow and often buggy. I have a fast and stable internet connection, yet it still takes forever to update or install apps. On top of that, Microsoft is pushing users toward using the Store instead of downloading apps directly from the internet, but the experience remains frustrating. Are there any plans to improve the performance and reliability of this service?", "author": "Friendly_Loquat_658", "created_time": "2025-05-11T09:50:21", "url": "https://reddit.com/r/microsoft/comments/1kjxds4/microsoft_store_servers/", "upvotes": 6, "comments_count": 1, "sentiment": "neutral", "engagement_score": 8.0, "source_subreddit": "microsoft", "hashtags": null, "ticker": "MSFT"}, {"platform": "reddit", "post_id": "reddit_1kk55n4", "title": "Apple is more anti-competitive than Google or Microsoft", "content": "On standard Ubuntu Linux Desktop go to Apple Maps site [https://maps.apple.com/](https://maps.apple.com/) and it will redirect you to [https://maps.apple.com/unsupported](https://maps.apple.com/unsupported) regardless which browser you use, Chrome, Edge or Firefox.", "author": "FortuneIIIPick", "created_time": "2025-05-11T16:36:13", "url": "https://reddit.com/r/microsoft/comments/1kk55n4/apple_is_more_anticompetitive_than_google_or/", "upvotes": 177, "comments_count": 30, "sentiment": "neutral", "engagement_score": 237.0, "source_subreddit": "microsoft", "hashtags": null, "ticker": "MSFT"}, {"platform": "reddit", "post_id": "reddit_1kk7l6u", "title": "Microsoft I have a design for the SMB market that will move us all to Azure", "content": "Major current issue - many companies in the SMB market are hybrid, full SaaS, or on prem.  The issue is that many SMB's need the security and GPO configuration where INTUNE or Azure AD falls very short of the classic GPO.  Call me old school but that can overall reduce security.\n\nMy Idea is that AD/DNS/GPO/File Services/Print Services can live in O365 where its almost 95% of what a current domain controller if not more.  On premises a low powered server/PC is where everything is cached and not on a full blown windows OS, give me a headless linux box or something, including the file server, and not sharepoint.  Charge 50-100$ a month for it, then an additional charge for storage and additional  20$ per extra server/PC to cache data.  \n\nWhat does this solve?  Quite a bit actually, if internet drops, file shares can still be accessed, if microsoft goes down, the business can still operate.  AD is in the microsoft cloud if the local servers both die and backups fail.  \n\nIf Microsft wants to kill off on prem and push everyone to azure I think this is the only answer, otherwise I think we are stuck in having on prem servers for a long time.  I for one would switch a good portion of our businesses to this design.  The Average business does not need a full blown on prem windows server anymore", "author": "blackjaxbrew", "created_time": "2025-05-11T18:21:24", "url": "https://reddit.com/r/microsoft/comments/1kk7l6u/microsoft_i_have_a_design_for_the_smb_market_that/", "upvotes": 2, "comments_count": 7, "sentiment": "bearish", "engagement_score": 16.0, "source_subreddit": "microsoft", "hashtags": null, "ticker": "MSFT"}]