[{"platform": "reddit", "post_id": "reddit_1ibobty", "title": "is it possible to have microsoft family but only with the time limits?", "content": "Awhile back my father put microsoft family on my computer, only for the time limit though. With microsoft family you’re going to have restrictions too, is there a way to have only the time limits and no restrictions?", "author": "Kind_Lie6930", "created_time": "2025-01-28T00:05:20", "url": "https://reddit.com/r/microsoft/comments/1ibobty/is_it_possible_to_have_microsoft_family_but_only/", "upvotes": 31, "comments_count": 3, "sentiment": "neutral", "engagement_score": 37.0, "source_subreddit": "microsoft", "hashtags": null, "ticker": "MSFT"}, {"platform": "reddit", "post_id": "reddit_1ibqpe5", "title": "Amazon Takes Office Space at New Tower in Miami's Wynwood Area - Bloomberg.com", "content": "", "author": "AmazonNewsBot", "created_time": "2025-01-28T02:00:03", "url": "https://reddit.com/r/amazon/comments/1ibqpe5/amazon_takes_office_space_at_new_tower_in_miamis/", "upvotes": 8, "comments_count": 0, "sentiment": "neutral", "engagement_score": 8.0, "source_subreddit": "amazon", "hashtags": null, "ticker": "MSFT"}, {"platform": "reddit", "post_id": "reddit_1ibxnzp", "title": "Microsoft CEO <PERSON><PERSON><PERSON> calls himself ‘product’ of bond between India-US", "content": "", "author": "HindustanTimes", "created_time": "2025-01-28T09:15:47", "url": "https://reddit.com/r/microsoft/comments/1ibxnzp/microsoft_ceo_satya_na<PERSON><PERSON>_calls_himself_product/", "upvotes": 297, "comments_count": 35, "sentiment": "bullish", "engagement_score": 367.0, "source_subreddit": "microsoft", "hashtags": null, "ticker": "MSFT"}, {"platform": "reddit", "post_id": "reddit_1ic2f8k", "title": "Seeking Insights on New Grad Remote Role Relocating", "content": "I’m a recent graduate starting a fully remote role at Microsoft. To boost my early career, I'm considering relocating from the East Coast (DC) to Seattle or Chicago. \n\nI loved my internship in Redmond. I was drawn to Seattle’s vibrant city life, excellent public transit, and stunning nature. The PNW spring/summer climate fits my preference for weather, and being near Microsoft HQ could enhance my learning and networking opportunities.\n\nOn the other hand, Chicago also offers a dynamic tech scene and a large Microsoft office. As a sports fan, I appreciate that both cities are major sports hubs.\n\nI’d love to hear from anyone with experience as a new grad remote worker in Seattle, Chicago, or similar cities. How have your experiences been with learning opportunities, networking, and work-life balance? How did living in your city influence your ability to make friends and build a professional network? Thanks for your insights!", "author": "NickoHand", "created_time": "2025-01-28T14:12:27", "url": "https://reddit.com/r/microsoft/comments/1ic2f8k/seeking_insights_on_new_grad_remote_role/", "upvotes": 2, "comments_count": 0, "sentiment": "neutral", "engagement_score": 2.0, "source_subreddit": "microsoft", "hashtags": null, "ticker": "MSFT"}, {"platform": "reddit", "post_id": "reddit_1icaefj", "title": "Client questionnaire and dependent outcome", "content": "Hi Guys. \n\nAfter some advice, please. Which programs or platforms within the Microsoft architecture (or 3rd party) should I use for the following use case.\n\nI would like to send a questionnaire out to customers who then answer a series of questions which, dependent on their answers, result in them being linked to a particular set of services unique to their answers.\n\nIs it a case of using any of these maybe?\n\n-  ‘Forms (with branching )?\n- Power automation? \n\nIf I’m honest, I don’t really understand how to use Forms even yet , so go easy on the technical side! \n\nThanks\n\n<PERSON>.", "author": "DannykGolf1979", "created_time": "2025-01-28T19:47:20", "url": "https://reddit.com/r/microsoft/comments/1icaefj/client_questionnaire_and_dependent_outcome/", "upvotes": 0, "comments_count": 0, "sentiment": "neutral", "engagement_score": 0.0, "source_subreddit": "microsoft", "hashtags": null, "ticker": "MSFT"}]