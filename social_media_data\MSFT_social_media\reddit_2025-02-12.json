[{"platform": "reddit", "post_id": "reddit_1ingakd", "title": "Azure AI as a bilingual screen reader?", "content": "How is Azure AI's TTS feature for bilingual texts, specifically ones written in both English and Italian? I'm in need of a good screen reader and would appreciate any advice. TIA!", "author": "Blissful<PERSON>utton", "created_time": "2025-02-12T02:15:51", "url": "https://reddit.com/r/microsoft/comments/1ingakd/azure_ai_as_a_bilingual_screen_reader/", "upvotes": 0, "comments_count": 0, "sentiment": "neutral", "engagement_score": 0.0, "source_subreddit": "microsoft", "hashtags": null, "ticker": "MSFT"}, {"platform": "reddit", "post_id": "reddit_1inlhlg", "title": "Microsoft Team Matching Odds?", "content": "I recently interviewed for a software engineer I role on some new AI team (I didn’t get a ton of information about the team during interviews) and finished the final round 2 weeks ago. Today I received an email from a recruiter saying this:\n\n“I’ve been able to confirm the results from your interview with us. We do not have an existing role for you at this time. However, you will remain eligible for placement on a new team if headcount becomes available within the next 6 months.”\n\nI was wondering what are the odds that they find a role for me. Not sure if I should keep interviewing or not. I do work as a software engineer for another company so not like it would hurt very much by waiting but I do want to leave my company sometime soon.", "author": "No_Value1340", "created_time": "2025-02-12T07:19:53", "url": "https://reddit.com/r/microsoft/comments/1inlhlg/microsoft_team_matching_odds/", "upvotes": 6, "comments_count": 21, "sentiment": "neutral", "engagement_score": 48.0, "source_subreddit": "microsoft", "hashtags": null, "ticker": "MSFT"}, {"platform": "reddit", "post_id": "reddit_1innag4", "title": "<PERSON> says he wants to 'turn warfighters into technomancers' as <PERSON><PERSON><PERSON> takes over production of the US Army's IVAS AR headset from Microsoft", "content": "Microsoft will continue to support IVAS functionality with \"advanced cloud infrastructure and AI capabilities,\" but it's out of the hardware game.", "author": "ControlCAD", "created_time": "2025-02-12T09:35:10", "url": "https://reddit.com/r/microsoft/comments/1innag4/palmer_luckey_says_he_wants_to_turn_warfighters/", "upvotes": 88, "comments_count": 2, "sentiment": "neutral", "engagement_score": 92.0, "source_subreddit": "microsoft", "hashtags": null, "ticker": "MSFT"}, {"platform": "reddit", "post_id": "reddit_1inryh9", "title": "Windows Upsell (Windows Update)", "content": "The amount of upselling after Windows Updates is getting ridiculous.\n\n[https://ibb.co/album/xm8CqX](https://ibb.co/album/xm8CqX) ", "author": "slfyst", "created_time": "2025-02-12T14:24:17", "url": "https://reddit.com/r/microsoft/comments/1inryh9/windows_upsell_windows_update/", "upvotes": 12, "comments_count": 26, "sentiment": "bearish", "engagement_score": 64.0, "source_subreddit": "microsoft", "hashtags": null, "ticker": "MSFT"}, {"platform": "reddit", "post_id": "reddit_1inv4pu", "title": "Question about <PERSON><PERSON><PERSON>", "content": "I do not subscribe to 365.  It doesn't make sense as to why I would subscribe to something when I can just buy it outright, knowing I'll constantly be using it...saves money in the long run.  I bout myself a Microsoft Office license key.\n\n  \nI know they're incorporating copilot into Word 365, but anyone know if it's also available if you bought a license key, rather then 365 subscription ?  After all, I did pay for it, I just went the less expensive option.", "author": "fieldday1982", "created_time": "2025-02-12T16:37:59", "url": "https://reddit.com/r/microsoft/comments/1inv4pu/question_about_copilot/", "upvotes": 0, "comments_count": 5, "sentiment": "bullish", "engagement_score": 10.0, "source_subreddit": "microsoft", "hashtags": null, "ticker": "MSFT"}, {"platform": "reddit", "post_id": "reddit_1io21tw", "title": "Why do you not want me to like you?", "content": "Why is the Microsoft suite getting less useful with every iteration?  My three pet peeves for today are \n\n* the inability to drag and drop email attachments from Outlook directly to SharePoint online\n   * You wrote both sets of software.  At no time during the development did anyone raise the possibility that uses might want this functionality.  I refuse to believe that a company of this size did not see that possibility and be able to solve this simple task.\n   * Why are you making it so difficult?\n* what happened to Add to Dictionary when spell checking in Word or Outlook?\n   * There is no ability to do that while in the flow of the task.  You have to stop what you're doing and follow a convoluted process of copy/pasting the word into the dictionary?\n   * Why deprecate a feature used by millions and not replace it with something better\n   * Why?\n* In Windows 10 start menu you could mouse over the app icon, and it would show you the last X files opened with that app.\n   * This was great, file opened in one fluid motion.\n   * Windows 11 - GONE.  You need to open the app THE<PERSON> find the file from the recent dialogue. \n   * Recent may or may not include the file you had just saved before lunch.  Random recents?\n\nWhat else have we lost that makes our lives more difficult?", "author": "CountryTraining", "created_time": "2025-02-12T21:18:23", "url": "https://reddit.com/r/microsoft/comments/1io21tw/why_do_you_not_want_me_to_like_you/", "upvotes": 8, "comments_count": 10, "sentiment": "bearish", "engagement_score": 28.0, "source_subreddit": "microsoft", "hashtags": null, "ticker": "MSFT"}]