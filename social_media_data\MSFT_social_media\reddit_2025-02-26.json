[{"platform": "reddit", "post_id": "reddit_1iydal1", "title": "Best time to get xbox in employee store?", "content": "Recently joined and I noticed the hardware with employee discount is not that great for xbox... just curious if there are special deals or other times where prices could go down by a lot? Even refurbished xboxs doesn't seem that much cheaper..", "author": "Lioil1", "created_time": "2025-02-26T02:45:19", "url": "https://reddit.com/r/microsoft/comments/1iydal1/best_time_to_get_xbox_in_employee_store/", "upvotes": 0, "comments_count": 13, "sentiment": "neutral", "engagement_score": 26.0, "source_subreddit": "microsoft", "hashtags": null, "ticker": "MSFT"}, {"platform": "reddit", "post_id": "reddit_1iyo9le", "title": "Azure, Power BI, and QuickBooks Online", "content": "I do real estate development and use quickbooks online for accounting. For each project, I set up a new QBO company (...I have many). I'd like to aggregate the data from all entities as the chart of accounts and activity is largely the same company-to-company. Is the best solution to store the data in an Azure SQL database ? I'd also like to connect to my data to Power BI for visualization.\n\nSecondly, I have no experience with Azure or SQL. Is this something I can set up myself or will need to outsource to a software engineer? I've been trying to connect to Intuit's API endpoints from Power Query and that's been challenging itself.\n\nThanks!", "author": "Tekell09", "created_time": "2025-02-26T14:05:37", "url": "https://reddit.com/r/microsoft/comments/1iyo9le/azure_power_bi_and_quickbooks_online/", "upvotes": 1, "comments_count": 0, "sentiment": "neutral", "engagement_score": 1.0, "source_subreddit": "microsoft", "hashtags": null, "ticker": "MSFT"}, {"platform": "reddit", "post_id": "reddit_1iz0or0", "title": "Time to hear back after final interviews", "content": "Hi,\n\nI had my final interviews for an SDE2 role (Azure Engineering) 2 weeks ago (Feb 10-11), and haven't heard back (Feb 26 today). I emailed the recruiter a week ago, and again on Feb 24, but no response. There were 2 headcounts for the same role, and my interviews went well, so it would be a bit surprising to get rejected.\n\nIt would be very helpful if someone could give some information about what this complete silence could mean, and what my chances are of getting an offer.\n\nI understand they may be interviewing other candidates, or keeping me as a backup, or having internal delays. No response to 2 follow-ups, and having waited for 2+ weeks feels excruciating though, and I would appreciate any insider information.\n\nThanks!\n\nUpdate: Heard back after 3 weeks and they've paused the hiring on this position.", "author": "earthling-banana", "created_time": "2025-02-26T22:48:24", "url": "https://reddit.com/r/microsoft/comments/1iz0or0/time_to_hear_back_after_final_interviews/", "upvotes": 23, "comments_count": 19, "sentiment": "neutral", "engagement_score": 61.0, "source_subreddit": "microsoft", "hashtags": null, "ticker": "MSFT"}]