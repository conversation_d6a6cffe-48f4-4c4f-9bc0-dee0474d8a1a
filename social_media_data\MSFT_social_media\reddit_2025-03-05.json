[{"platform": "reddit", "post_id": "reddit_1j3u0y6", "title": "Audit tool against Best Practices", "content": "Hi All,\n\nI’m currently working for a MSP that’s looking after a few clients that consume a whole host of Microsoft 365 products and services, such as: exchange online, m365 apps, intune, teams, SharePoint, OneDrive, entra, security etc. \n\nI was wondering if there’s a tool out there - whether it’s 1 tool or a few tools combined, that can provide me with a host of recommendations to update/check against a tenant? \n\nThis would be ideal if it also generated a report that had all the good/bad/ugly in the tenant. \n\nI am thinking along the lines of ORCA but more feature and service rich.\n\nKeen to hear how you guys do it. \n\nThanks. ", "author": "fungusfromamongus", "created_time": "2025-03-05T03:39:19", "url": "https://reddit.com/r/microsoft/comments/1j3u0y6/audit_tool_against_best_practices/", "upvotes": 1, "comments_count": 3, "sentiment": "bullish", "engagement_score": 7.0, "source_subreddit": "microsoft", "hashtags": null, "ticker": "MSFT"}, {"platform": "reddit", "post_id": "reddit_1j3u2en", "title": "Who is responsible for the Hiring Decision?", "content": "Hi everybo<PERSON>, I went to interview loop with Microsoft for Technology Specialist Intern, went for two interviews back to back, I think feedback was great. I think the last interview the person was significantly more senior. But who actually makes the hiring decision, is it <PERSON><PERSON> or the last guy in the interview loop? Thank you everyone", "author": "NewAppointment2190", "created_time": "2025-03-05T03:41:34", "url": "https://reddit.com/r/microsoft/comments/1j3u2en/who_is_responsible_for_the_hiring_decision/", "upvotes": 0, "comments_count": 16, "sentiment": "neutral", "engagement_score": 32.0, "source_subreddit": "microsoft", "hashtags": null, "ticker": "MSFT"}, {"platform": "reddit", "post_id": "reddit_1j4f8kl", "title": "Microsoft 365 is so bad", "content": "Shifted to a new company that uses Microsoft 365.\nEverything in the suite is a hot pile of garbage.\n\n- Teams doesn’t send mobile notifications\n- outlook is extremely unintuitive and search sucks\n- I can’t manage separate mails according to custom tags, need to follow the stupid coloring system. If there is a way it’s hidden somewhere and couldn’t find out where even after googling it\n- one note is horrible, almost never pastes images on one attempt, syncing always has issues.\n- SharePoint is its own UI made for people who hate finding files.\n- copilot is absolutely useless, unable to hold context\n- calendar feels like a Nokia era calendar, doesn’t seem to integrate with personal accounts or other providers\n\nBasically everything sucks and yet they keep shipping new features without addressing any of the complaints in the existing software.", "author": "No-Equipment5090", "created_time": "2025-03-05T21:57:09", "url": "https://reddit.com/r/microsoft/comments/1j4f8kl/microsoft_365_is_so_bad/", "upvotes": 0, "comments_count": 18, "sentiment": "neutral", "engagement_score": 36.0, "source_subreddit": "microsoft", "hashtags": null, "ticker": "MSFT"}]