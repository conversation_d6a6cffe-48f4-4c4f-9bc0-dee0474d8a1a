[{"platform": "reddit", "post_id": "reddit_1jq5s7m", "title": "Positive Feedback but Rejected Twice after Onsite - Software Engineering", "content": "Hello all! I applied to Microsoft as an SDE in November, interviewed in December, and had a full loop/onsite scheduled in January. The onsite went well, with a few hiccups, but after three weeks I received a rejection letter. Both the recruiter and the hiring manager said my feedback was very positive and informed me that the decision was close, but they chose another candidate due to a small differential. The HM's feedback on the differential was clear and actionable.\n\nThe recruiter has been amazing. They helped me find another role, which I also interviewed for onsite. I felt like I performed better this time (ensuring that I applying the previous feedback), but I received another rejection last week. Again, the feedback was positive, but they found someone who was a better fit.\n\nDespite the rejections, the recruiter mentioned that my feedback remains positive and that they can connect me with other roles I'm interested in. Has anyone experienced something similar but eventually landed a role? Does the positive feedback and the experience imply that I have been voted for hire but not selected? And does this mean I could be a in a pool of candidates where I may get selected from in the future?", "author": "mkat199", "created_time": "2025-04-03T01:30:48", "url": "https://reddit.com/r/microsoft/comments/1jq5s7m/positive_feedback_but_rejected_twice_after_onsite/", "upvotes": 24, "comments_count": 4, "sentiment": "bullish", "engagement_score": 32.0, "source_subreddit": "microsoft", "hashtags": null, "ticker": "MSFT"}, {"platform": "reddit", "post_id": "reddit_1jqhsgq", "title": "Microsoft celebrates 50 years", "content": "", "author": "<PERSON><PERSON><PERSON>", "created_time": "2025-04-03T12:59:47", "url": "https://reddit.com/r/microsoft/comments/1jqhsgq/microsoft_celebrates_50_years/", "upvotes": 118, "comments_count": 20, "sentiment": "neutral", "engagement_score": 158.0, "source_subreddit": "microsoft", "hashtags": null, "ticker": "MSFT"}, {"platform": "reddit", "post_id": "reddit_1jqjn1k", "title": "Young People Don't Want to Use Microsoft 365: Can Copilot Win Them Back With AI?", "content": "", "author": "Hot_Transportation87", "created_time": "2025-04-03T14:17:45", "url": "https://reddit.com/r/microsoft/comments/1jqjn1k/young_people_dont_want_to_use_microsoft_365_can/", "upvotes": 0, "comments_count": 2, "sentiment": "neutral", "engagement_score": 4.0, "source_subreddit": "microsoft", "hashtags": null, "ticker": "MSFT"}, {"platform": "reddit", "post_id": "reddit_1jqnvz6", "title": "Microsoft releases wallpapers to celebrate 50 anniversary", "content": "4 wallpapers to celebrate Microsoft's 50 anniversary!\n\n[https://blogs.windows.com/windowsexperience/2025/04/03/windows-wallpapers-worth-celebrating/](https://blogs.windows.com/windowsexperience/2025/04/03/windows-wallpapers-worth-celebrating/)", "author": "Yet_Another_RD_User", "created_time": "2025-04-03T17:03:10", "url": "https://reddit.com/r/microsoft/comments/1jqnvz6/microsoft_releases_wallpapers_to_celebrate_50/", "upvotes": 22, "comments_count": 10, "sentiment": "neutral", "engagement_score": 42.0, "source_subreddit": "microsoft", "hashtags": null, "ticker": "MSFT"}, {"platform": "reddit", "post_id": "reddit_1jqpb1v", "title": "Microsoft kills the iconic Blue Screen of Death. It looks like this now", "content": "It's now the Black or Green Screen of Death!! ", "author": "LordKrazyMoos<PERSON>", "created_time": "2025-04-03T17:56:59", "url": "https://reddit.com/r/microsoft/comments/1jqpb1v/microsoft_kills_the_iconic_blue_screen_of_death/", "upvotes": 33, "comments_count": 14, "sentiment": "neutral", "engagement_score": 61.0, "source_subreddit": "microsoft", "hashtags": null, "ticker": "MSFT"}, {"platform": "reddit", "post_id": "reddit_1jqrvtb", "title": "Team switch at Msft India", "content": "Hi all,\n\nI got a offer from Microsoft Bangalore but my home town is in Hyderabad. For me, its really challenging to leave my hometown due to personal reasons. I wanted MSFT Hyderabad offer.\n\nCan you suggest how should I approach this situation from team switch perspective; and how soon after joining MSFT I can apply for team change. Do internal candidates gets preference over someone who is an external applicant? I am ready to sit for interviews for internal team switch.\n\nThanks in advance :)", "author": "Fit_Competition_9194", "created_time": "2025-04-03T19:34:43", "url": "https://reddit.com/r/microsoft/comments/1jqrvtb/team_switch_at_msft_india/", "upvotes": 2, "comments_count": 3, "sentiment": "neutral", "engagement_score": 8.0, "source_subreddit": "microsoft", "hashtags": null, "ticker": "MSFT"}, {"platform": "reddit", "post_id": "reddit_1jqslhu", "title": "Where can I find the full clipart audio library from Office 2007?", "content": "I remember when I was a kid, I used to mess around a lot in PowerPoint with the clipart tool. It had a lot of sound effects you could put in.\n\nI've found these slideshows I made since then, and many of the sounds are missing. Some still exist and play normally, and I was able to find some in an archive of clipart and sounds from [archive.org: ](http://archive.org)<https://archive.org/details/MS_Clipart_Collection>\n\nThat being said, there are many I distinctly remember that are missing from the PowerPoints I made. I remember a rock song called \"Mr Fat Face\" and a rap song called \"Nerds Fly Low\" or something. (My memory might be foggy).\n\nWhere can I find a complete library of these sounds? Searching the archive, I can find some of the ones I used, but only the ones that still exist and play normally on the old files. What happened to the missing ones? Can I find them anywhere? Why aren't they on any archives?", "author": "Cool-Delivery-3773", "created_time": "2025-04-03T20:02:10", "url": "https://reddit.com/r/microsoft/comments/1jqslhu/where_can_i_find_the_full_clipart_audio_library/", "upvotes": 1, "comments_count": 2, "sentiment": "neutral", "engagement_score": 5.0, "source_subreddit": "microsoft", "hashtags": null, "ticker": "MSFT"}, {"platform": "reddit", "post_id": "reddit_1jqtsys", "title": "Microsoft pulls back on data centers from Chicago to Jakarta", "content": "", "author": "esporx", "created_time": "2025-04-03T20:48:28", "url": "https://reddit.com/r/microsoft/comments/1jqtsys/microsoft_pulls_back_on_data_centers_from_chicago/", "upvotes": 7, "comments_count": 1, "sentiment": "neutral", "engagement_score": 9.0, "source_subreddit": "microsoft", "hashtags": null, "ticker": "MSFT"}, {"platform": "reddit", "post_id": "reddit_1jqwu1o", "title": "Microsoft 365 is horrible", "content": "Gone are the good ol’ days when one could download software for a one time purchase price and use these simple programs to perform tasks that computers were created to do. \nYou are provided a plethora of options increasing in price, I purchased 365 home to get the basics. To find out that you are not even able to open downloaded files.an office program that will not open files seems insane. \n\nPerhaps there is a way to do this, one would think that a quick call to customer support would have you up and running in no time, guess again, you’re call is answered by an automated service that directs you to online support where you have to sift through old questions that doesn’t pertain specifically to your issue. \n\nWhy has a simple program that has been used and loved since the dawn of home computing been perverted into a watered down, hard to navigate money trap that doesn’t perform basic tasks.\n\nAll forms of business are adopting a similar approach of doing business and it is absolutely infuriating.", "author": "Big_D_493", "created_time": "2025-04-03T22:54:15", "url": "https://reddit.com/r/microsoft/comments/1jqwu1o/microsoft_365_is_horrible/", "upvotes": 0, "comments_count": 18, "sentiment": "neutral", "engagement_score": 36.0, "source_subreddit": "microsoft", "hashtags": null, "ticker": "MSFT"}]