[{"platform": "reddit", "post_id": "reddit_1ja1p1p", "title": "Microsoft refusing to sort out their messy OneDrive server migration", "content": "I've spent the past week unable to access my OneDrive files. I've extensively tried Microsoft support to no avail. Every file is visible in OneDrive in the browser, but clicking on any file leads to \"500 internal server error\". I have been through an absurdly thorough troubleshooting routine. \n\nMicrosoft is apparently aware of this exact issue affecting a significant number of users due to a server migration, but is not willing to do any workarounds or provide access in a timely manner. Instead, customers without access to OneDrive are supposed to wait until \"mid-March\" for a resolution.\n\nI am angry and incredibly disappointed that Microsoft mishandled a routine server migration and that Microsoft is still unable to provide me access to my files. Cloud storage is supposed to be reliable, but apparently we need multiple cloud AND physical backups. I shouldn't have to spend a week getting fobbed off by tech support and told it's my fault, before being told this is a widespread issue and there's nothing anyone can do except wait.", "author": "Mapleaf42", "created_time": "2025-03-13T02:23:06", "url": "https://reddit.com/r/microsoft/comments/1ja1p1p/microsoft_refusing_to_sort_out_their_messy/", "upvotes": 0, "comments_count": 13, "sentiment": "neutral", "engagement_score": 26.0, "source_subreddit": "microsoft", "hashtags": null, "ticker": "MSFT"}, {"platform": "reddit", "post_id": "reddit_1jab2ym", "title": "AWS / Microsoft level conversion", "content": "Hi all, I recently applied and interviewed for a data center technician role. I'm an L3 DCT in AWS but it's hard to gauge whether this role is equivalent or under? \n\nI tried asking one of the hiring managers what it could equate to but they didn't really have a response? \n\nSomething AWS recently started doing is only hiring L2 and then a year of trainee status before promo to L3. \n\n", "author": "Campfire-9009", "created_time": "2025-03-13T12:44:01", "url": "https://reddit.com/r/microsoft/comments/1jab2ym/aws_microsoft_level_conversion/", "upvotes": 2, "comments_count": 3, "sentiment": "neutral", "engagement_score": 8.0, "source_subreddit": "microsoft", "hashtags": null, "ticker": "MSFT"}, {"platform": "reddit", "post_id": "reddit_1jaekcn", "title": "Have you used the Microsoft Zero Trust Framework Deployment tool?", "content": "I'm excited to learn more about the #microsoft #zerotrust #MAM tooling in today's latest Microsoft Security Customer Connection Program (CCP) call!  \n  \nCheck it out here: [https://microsoft.github.io/zerotrustassessment](https://microsoft.github.io/zerotrustassessment)  \n  \nIt helps walk you or your clients through the Microsoft Zero Trust Framework deployment.  \n  \nMicrosoft Security Customer Connection Program join here [www.aka.ms/JoinCCP](www.aka.ms/JoinCCP)", "author": "CPFCoaching", "created_time": "2025-03-13T15:25:47", "url": "https://reddit.com/r/microsoft/comments/1jaekcn/have_you_used_the_microsoft_zero_trust_framework/", "upvotes": 2, "comments_count": 3, "sentiment": "neutral", "engagement_score": 8.0, "source_subreddit": "microsoft", "hashtags": null, "ticker": "MSFT"}, {"platform": "reddit", "post_id": "reddit_1jajhcz", "title": "Commuting to Microsoft from Fremont", "content": "I’ll be interning at Microsoft this summer and found a decent place in Fremont and I heard there is a shuttle that makes stops near there. Would this be a reasonable commute to do every day? How long would it take? Is there any flexibility for remote work? Thanks!!", "author": "Independent-Ground40", "created_time": "2025-03-13T18:48:13", "url": "https://reddit.com/r/microsoft/comments/1jajhcz/commuting_to_microsoft_from_fremont/", "upvotes": 2, "comments_count": 3, "sentiment": "bullish", "engagement_score": 8.0, "source_subreddit": "microsoft", "hashtags": null, "ticker": "MSFT"}, {"platform": "reddit", "post_id": "reddit_1jak223", "title": "is it worth moving from Amazon to MSFT", "content": "Hey everyone,\n\nI’m currently an L6 Finance Manager at Amazon HQ2 and have been with the company for three years. I recently received an offer for an L62 Senior Finance Manager role at Microsoft. The total comp ranges from $180k-$201k, which is pretty comparable to where I’m at now.\n\nHonestly, I’m feeling pretty burnt out at Amazon so a change would be welcome\n\nThe tricky part? The Microsoft role was originally supposed to be based in Reston but now they want it to be in Redmond. I’m from the East Coast, with all my family within an hour’s drive, so while I am open to moving to the West Coast I would be moving to a location where I would be completely alone.\n\nI’m open to relocating, but I’m torn between a fresh start and staying close to my support system.\n\nAnyone else made a similar move? Is the switch to Microsoft worth it, especially considering the relocation? Would love to hear your thoughts or any advice on how to approach this decision!", "author": "Novel_Procedure_119", "created_time": "2025-03-13T19:12:11", "url": "https://reddit.com/r/microsoft/comments/1jak223/is_it_worth_moving_from_amazon_to_msft/", "upvotes": 131, "comments_count": 60, "sentiment": "neutral", "engagement_score": 251.0, "source_subreddit": "microsoft", "hashtags": null, "ticker": "MSFT"}, {"platform": "reddit", "post_id": "reddit_1jamm89", "title": "One Drive (or \"Microsoft 365 Personal\") has jumped from £59.99 to £84.99 - a 42% increase, why?", "content": "Is anyone else shocked by this massive price hike? \n\nI've been using it for years to keep my documents backed up - mostly family pictures, payslips, and random other documents. \n\nI'm looking for alternatives as it seems Microsoft are charging you for AI features regardless of if you need them or not and there doesn't seem to be a normal plan. \n\nI found this site to [compare cloud storage providers](https://comparisontabl.es/cloud-storage/) \\- has anyone moved and who to?", "author": "OrangeRackso", "created_time": "2025-03-13T21:00:05", "url": "https://reddit.com/r/microsoft/comments/1jamm89/one_drive_or_microsoft_365_personal_has_jumped/", "upvotes": 0, "comments_count": 10, "sentiment": "neutral", "engagement_score": 20.0, "source_subreddit": "microsoft", "hashtags": null, "ticker": "MSFT"}, {"platform": "reddit", "post_id": "reddit_1jaobaw", "title": "10 year yield and govt shutdown", "content": "Hi! Is there reason for this hesitation to buy the 10 year? Yes it’s down today a little but even when <PERSON><PERSON> was in office at was 3.3 at the end of his presidency.. \n\nDoes the govt shutdown cause hesitancy for people to buy bonds? \n\nWith all the bad news..companies reporting slow down..it’s very clear we are on a path to recession like activity..and this isn’t even counting in the government slashing that will in turn cause more layoffs to contract workers via cancelling contracts ect..\n\nAnd FURTHER. The president, the secretary is treasury..and secretary of commerce ALL WANT THE TEN YEAR YIELD DOWN..\n\nHonestly if the ten year would go down to 3 I think all this fiasco would be done. \n\nAnyway trump still going full steam ahead to cause a recession quickly so he can have enough time to fix it. He is all but saying he is going to cause a recession. \n\nWill the 10 year fall harder after the govt shutdown is resolved? ", "author": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "created_time": "2025-03-13T22:13:05", "url": "https://reddit.com/r/investing_discussion/comments/1jaobaw/10_year_yield_and_govt_shutdown/", "upvotes": 3, "comments_count": 9, "sentiment": "bullish", "engagement_score": 21.0, "source_subreddit": "investing_discussion", "hashtags": null, "ticker": "MSFT"}]