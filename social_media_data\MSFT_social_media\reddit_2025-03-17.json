[{"platform": "reddit", "post_id": "reddit_1jdic51", "title": "The AI Server Gold Rush—$SMCI’s Biggest Growth Catalyst", "content": "Everyone is focused on AI models like ChatGPT, but **who is actually building the data centers that power them?**\n\n🔹 **Supermicro’s AI server revenue is expected to double to $14B in FY24**  \n🔹 **Liquid-cooled servers are becoming a necessity for high-performance AI workloads**  \n🔹 **Customers include NVIDIA, Microsoft, Meta, and OpenAI**\n\nAI growth isn’t slowing down, and $SMCI is one of **the only pure plays** in AI infrastructure. Are investors **underestimating its upside?**", "author": "qan23", "created_time": "2025-03-17T17:27:08", "url": "https://reddit.com/r/investing_discussion/comments/1jdic51/the_ai_server_gold_rushsmcis_biggest_growth/", "upvotes": 2, "comments_count": 3, "sentiment": "bullish", "engagement_score": 8.0, "source_subreddit": "investing_discussion", "hashtags": null, "ticker": "MSFT"}, {"platform": "reddit", "post_id": "reddit_1jdjxff", "title": "Xbox controller dead. Refusing to fix", "content": "I have an elite 2 controller.   It stopped working, will not charge or work even directly plugged in.  I sent for repair.  They did not repair and said water damage.     But my controller has never had anything spilled on it.  Never fell into any water.  I took care of it.  One night it was working just fine. The next day I went to use it, dead.   The only thing I can think of is that I live in Fl and we get some crazy high humidity.  But my controller is always inside so not sure that could even be it.  I have no kids that use it or anything like that either.  \n\nWhat could cause my controller to flag for water damage if it's never been exposed to water?     I'm out 200 bucks now.   Pretty bummed and not going to replace it with another one.     I've always been proud to be an Xbox owner.   But I'm thinking of converting to the dark side at this point.  It took me sending it in 3 times with them to even get the feedback of why it wasn't being repaired.     \n\nI'm a pretty honest guy and typically a fan of Microsoft.   A few years ago I was cleaning the pool and fell in while wearing my headphones (was listening to music on them).  Needless to say they didn't work anymore and I knew it was on me.   So I just ordered a new pair.   But for the controller.  I've never spilled on it, never got mad and tossed it, nothing but love for it.    ", "author": "Cyphergod247", "created_time": "2025-03-17T18:30:15", "url": "https://reddit.com/r/microsoft/comments/1jdjxff/xbox_controller_dead_refusing_to_fix/", "upvotes": 0, "comments_count": 7, "sentiment": "neutral", "engagement_score": 14.0, "source_subreddit": "microsoft", "hashtags": null, "ticker": "MSFT"}, {"platform": "reddit", "post_id": "reddit_1jdo4ef", "title": "Microsoft 365 personal package price increase", "content": "Did anybody get an email from Microsoft regarding a Huge price increase? Around 42% increase in the yearly subscription. It feels unreal.", "author": "BeautifulPrimary1949", "created_time": "2025-03-17T21:18:10", "url": "https://reddit.com/r/microsoft/comments/1jdo4ef/microsoft_365_personal_package_price_increase/", "upvotes": 1, "comments_count": 7, "sentiment": "neutral", "engagement_score": 15.0, "source_subreddit": "microsoft", "hashtags": null, "ticker": "MSFT"}, {"platform": "reddit", "post_id": "reddit_1jdpx85", "title": "Microsoft 365 Personal Classic Vs. Microsoft 365 Basic?", "content": "Hi,\n\nI didn't see anywhere else to ask this, but what's the difference between Microsoft 365 Personal Classic ($69.99 per year) vs. Microsoft 365 Basic (19.99) a year?\n\nWould Basic work fine for me using Word, PowerPoint, and not Outlook? Also, it's just for myself, no one else.\n\n  \nThx.", "author": "Derpolitik23", "created_time": "2025-03-17T22:34:59", "url": "https://reddit.com/r/microsoft/comments/1jdpx85/microsoft_365_personal_classic_vs_microsoft_365/", "upvotes": 3, "comments_count": 7, "sentiment": "neutral", "engagement_score": 17.0, "source_subreddit": "microsoft", "hashtags": null, "ticker": "MSFT"}]