[{"platform": "reddit", "post_id": "reddit_1iw5rz1", "title": "Xbox Series X Sales Halt in Brazil Sparks Debate Over Microsoft’s Hardware Future", "content": "", "author": "Zombotic69", "created_time": "2025-02-23T08:42:59", "url": "https://reddit.com/r/microsoft/comments/1iw5rz1/xbox_series_x_sales_halt_in_brazil_sparks_debate/", "upvotes": 35, "comments_count": 1, "sentiment": "neutral", "engagement_score": 37.0, "source_subreddit": "microsoft", "hashtags": null, "ticker": "MSFT"}, {"platform": "reddit", "post_id": "reddit_1iwehsu", "title": "Should I switch from Apple ecosystem to Windows laptop?", "content": "I’ve been using Apple products for as long as I can remember. Right now, I have a MacBook, iPhone, iPad, and AirPods. However, I’m considering replacing my MacBook with a gaming laptop to be able to play games and have more flexibility in software development. That said, I’m so used to the Apple ecosystem that the thought of switching feels overwhelming.\n\nIdeally, I’d like to have both a MacBook and a gaming laptop, but I don’t have an established setup yet and don’t want to allocate that much budget. I also considered getting a PS5, but since the games I play the most are LoL and HOI4, it wouldn’t really serve my needs.\n\nMy question is, has anyone made a similar switch? If so, what was your experience like? I think the features I would miss the most are AirPods' seamless switching and AirDrop. Other than that, I actually prefer Windows over macOS.\n\nI’ve been looking at the **Asus Zephyrus** since its design quality is close to the MacBook. But I’m unsure whether I’d regret switching because, for a non-gaming daily user, MacBooks are truly amazing devices. Still, as a student living alone abroad, I feel like I’d lose my mind if I couldn’t play games 😁.\n\nOne last thing to note: I’ll be studying **Computer Science**. I believe macOS would be sufficient, but would using Windows give me more flexibility as a developer?", "author": "Pluxy01", "created_time": "2025-02-23T16:51:17", "url": "https://reddit.com/r/microsoft/comments/1iwehsu/should_i_switch_from_apple_ecosystem_to_windows/", "upvotes": 4, "comments_count": 46, "sentiment": "neutral", "engagement_score": 96.0, "source_subreddit": "microsoft", "hashtags": null, "ticker": "MSFT"}, {"platform": "reddit", "post_id": "reddit_1iwetdm", "title": "Separate Calendar App", "content": "Hi!  I’m just curious, am I the only one who would like to see a calendar app that is actually separate from Outlook?  The Outlook calendar is what I use for my entire life, just to make it easy, and I guess I would love to skip the seeing my email first or going through Teams. Am I the only one?  Is there a functionality that I’m missing?  I’m fairly adept at Microsoft but certainly not an expert. ", "author": "v<PERSON><PERSON><PERSON>", "created_time": "2025-02-23T17:04:33", "url": "https://reddit.com/r/microsoft/comments/1iwetdm/separate_calendar_app/", "upvotes": 5, "comments_count": 6, "sentiment": "neutral", "engagement_score": 17.0, "source_subreddit": "microsoft", "hashtags": null, "ticker": "MSFT"}, {"platform": "reddit", "post_id": "reddit_1iwfnbe", "title": "With the upcoming Microsoft 365 price increase from roughly 70 dollars to 100 dollars, you're still able to keep the old subscription. Here's how:", "content": "Go to: [account.microsoft.com/services/microsoft365](http://account.microsoft.com/services/microsoft365)\n\nUnder Manage Subscription, select Cancel Subscription\n\nYou'll then have the option to switch back to your original plan, aka \"Microsoft 365 Personal Classic\"— without all the AI stuff Microsoft is pushing down your throat.\n\nPersonally I really don't need these kinda features, figured there might be more people that don't know about this.\n\nAfter switching back it'll charge you on your usual billing date.\n\nHope this helps you out!\n\n  \n\n\nedit: removed 'www.' from the link, which caused it not to work", "author": "RedAceBeetle", "created_time": "2025-02-23T17:39:59", "url": "https://reddit.com/r/microsoft/comments/1iwfnbe/with_the_upcoming_microsoft_365_price_increase/", "upvotes": 86, "comments_count": 20, "sentiment": "neutral", "engagement_score": 126.0, "source_subreddit": "microsoft", "hashtags": null, "ticker": "MSFT"}, {"platform": "reddit", "post_id": "reddit_1iwho4a", "title": "Google Cloud's quantum-safe encryption contrasts with Microsoft's advances", "content": "\n**Google Cloud is making strides in encryption safety with its quantum-safe digital signatures.** This recent update to the Cloud Key Management Service (Cloud KMS) addresses the imminent threats posed by quantum computing. As Microsoft also progresses in this arena with its Majorana 1 chip, the competition heats up in securing digital infrastructures.\n\nOrganizations leveraging cloud solutions must recognize the rapid advancements in cybersecurity technologies that companies like Google and Microsoft are introducing. Both tech giants are prioritizing encryption methods capable of resisting quantum attacks, highlighting an essential transition for businesses handling sensitive data. As users begin testing these new features, the focus remains on refining methods to combat emerging cyber threats effectively.\n\n- Google introduces quantum-safe digital signatures in Cloud KMS.\n\n- Microsoft advancing its own quantum computing solutions.\n\n- Both companies emphasize encryption's importance in cybersecurity.\n\n- Users encouraged to integrate new features seamlessly.\n\n[(View Details on PwnHub)](https://www.reddit.com/r/pwnhub/comments/1iwhlut/google_cloud_enhances_security_with_quantumsafe/)\n        ", "author": "<PERSON>-<PERSON>", "created_time": "2025-02-23T19:04:36", "url": "https://reddit.com/r/microsoft/comments/1iwho4a/google_clouds_quantumsafe_encryption_contrasts/", "upvotes": 2, "comments_count": 0, "sentiment": "neutral", "engagement_score": 2.0, "source_subreddit": "microsoft", "hashtags": null, "ticker": "MSFT"}, {"platform": "reddit", "post_id": "reddit_1iwhoar", "title": "Can we bring back Microsoft assistant characters instead of the search bar?", "content": "", "author": "laced1", "created_time": "2025-02-23T19:04:49", "url": "https://reddit.com/r/microsoft/comments/1iwhoar/can_we_bring_back_microsoft_assistant_characters/", "upvotes": 6, "comments_count": 8, "sentiment": "neutral", "engagement_score": 22.0, "source_subreddit": "microsoft", "hashtags": null, "ticker": "MSFT"}]