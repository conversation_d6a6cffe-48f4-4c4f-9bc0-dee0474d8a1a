[{"platform": "reddit", "post_id": "reddit_1j9cja4", "title": "Rejected or no?!", "content": "I interviewed for an internal transfer on the last week of February, but I still haven’t heard back from the recruiter. I’ve followed up twice via email, but there’s been no response so far. Does Microsoft typically notify candidates if they’ve been rejected?", "author": "<PERSON><PERSON>-Engineer", "created_time": "2025-03-12T05:45:38", "url": "https://reddit.com/r/microsoft/comments/1j9cja4/rejected_or_no/", "upvotes": 0, "comments_count": 4, "sentiment": "neutral", "engagement_score": 8.0, "source_subreddit": "microsoft", "hashtags": null, "ticker": "MSFT"}, {"platform": "reddit", "post_id": "reddit_1j9o0qq", "title": "A years-long scam that began with fake Windows pop-ups ends with PayPal payments: Con artists in Cyprus took advantage of Windows users through a scam designed to create multiple transactions — all based on malware that didn't exist.", "content": "", "author": "wind_of_pain", "created_time": "2025-03-12T16:25:49", "url": "https://reddit.com/r/microsoft/comments/1j9o0qq/a_yearslong_scam_that_began_with_fake_windows/", "upvotes": 9, "comments_count": 0, "sentiment": "bullish", "engagement_score": 9.0, "source_subreddit": "microsoft", "hashtags": null, "ticker": "MSFT"}, {"platform": "reddit", "post_id": "reddit_1j9qtts", "title": "Can I work remotely as a new grad for Azure?", "content": "If I potentially get a ft offer from an Azure team, can I work remotely from across the country as a new grad?", "author": "No-Tangelo-1857", "created_time": "2025-03-12T18:19:26", "url": "https://reddit.com/r/microsoft/comments/1j9qtts/can_i_work_remotely_as_a_new_grad_for_azure/", "upvotes": 1, "comments_count": 8, "sentiment": "neutral", "engagement_score": 17.0, "source_subreddit": "microsoft", "hashtags": null, "ticker": "MSFT"}, {"platform": "reddit", "post_id": "reddit_1j9tb15", "title": "Is working at Microsoft as a software engineer really that stressful?", "content": "Hello everyone, this is not a personal experience but rather a close one. My partner, with whom I live, has been working at Microsoft as a software engineer for a little over a year. Ever since he joined, everything has changed—he’s stressed all the time, constantly works overtime, even on holidays and weekends. When he’s on call, he doesn’t sleep well or eat properly. He tells me that his teammates are not very collaborative or willing to help; they are more individualistic.\n\nI even overheard a meeting with his direct manager once, where the manager asked if they felt pressured. Everyone answered yes, and the manager simply said that’s just Microsoft’s culture, that there was nothing to do but adapt… It makes me really sad to see how my partner has changed in what we initially thought would be his dream job. The benefits are excellent, but I don’t think they make up for the physical and emotional toll it has taken on him and our relationship.\n\nI’m also a software engineer, and of course, at some point in my career, I dreamed of working at a big tech company like Microsoft. I wonder, is it really this bad for everyone? Or did we just have bad luck?\n\nThank you for your replies ", "author": "LoFiSloth", "created_time": "2025-03-12T19:59:29", "url": "https://reddit.com/r/microsoft/comments/1j9tb15/is_working_at_microsoft_as_a_software_engineer/", "upvotes": 142, "comments_count": 70, "sentiment": "neutral", "engagement_score": 282.0, "source_subreddit": "microsoft", "hashtags": null, "ticker": "MSFT"}, {"platform": "reddit", "post_id": "reddit_1j9u6is", "title": "Microsoft new billing experience is bugged", "content": "We're currently experiencing a major issue with Microsoft's new billing system, which prevents us from purchasing certain new services. Despite having transitioned from the old MOSA (Microsoft Online Subscription Agreement) to the new MCA (Microsoft Customer Agreement), our account appears to be stuck in limbo between these two billing experiences.\n\nWhen attempting to buy a service that requires MCA, we're prompted to create a new billing profile—even though we've already set one up via Azure. Strangely, this billing profile doesn't appear in the Microsoft Admin Center at all.\n\nWe've had multiple sessions with Microsoft support, including several screen-sharing meetings. However, support has so far been unable to resolve the issue. The support ticket remains open, and we continue to receive daily emails indicating they're still working on a solution. Occasionally, support contacts us for further screen-sharing sessions, but unfortunately, the issue persists.\n\nEven more concerning is the possibility that the billing system may no longer be generating new invoices for our existing Microsoft Business Premium subscriptions. While being unable to purchase additional services is problematic, the risk of disruption to billing for existing subscriptions is deeply troubling.\n\nIt's genuinely frustrating that Microsoft acknowledges the issue yet seems incapable of resolving it—even after weeks of continuous effort. This isn't a criticism of the individual support representatives, who clearly lack the necessary permissions to fix the issue. Rather, the problem appears to stem from higher-level flaws within Microsoft's extraordinarily bureaucratic structure, compounded by the release of a severely bugged new billing experience.\n\nHas anyone else encountered this issue or something similar? Any advice or shared experiences would be greatly appreciated.", "author": "Darius991", "created_time": "2025-03-12T20:35:28", "url": "https://reddit.com/r/microsoft/comments/1j9u6is/microsoft_new_billing_experience_is_bugged/", "upvotes": 5, "comments_count": 3, "sentiment": "bullish", "engagement_score": 11.0, "source_subreddit": "microsoft", "hashtags": null, "ticker": "MSFT"}]