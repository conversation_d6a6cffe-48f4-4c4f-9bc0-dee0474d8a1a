[{"platform": "reddit", "post_id": "reddit_1ih93nt", "title": "Windows 10 doesn't have the middle-finger emoji", "content": "I was digging around the emoji shortcut thingy, and I couldn't find the middle-finger emoji. I guess they don't want people to be offensive or smt.   \n  \nP.S: I only posted this here bc Windows 10 subreddit probably has enough people complaining about things like ads, telemetry, etc.", "author": "Great_Leg_4836", "created_time": "2025-02-04T04:04:10", "url": "https://reddit.com/r/microsoft/comments/1ih93nt/windows_10_doesnt_have_the_middlefinger_emoji/", "upvotes": 1, "comments_count": 4, "sentiment": "bearish", "engagement_score": 9.0, "source_subreddit": "microsoft", "hashtags": null, "ticker": "MSFT"}, {"platform": "reddit", "post_id": "reddit_1ihfwvj", "title": "Interview with OG Microsoft DevDiv lead <PERSON><PERSON>", "content": "", "author": "itsemdee", "created_time": "2025-02-04T11:51:22", "url": "https://reddit.com/r/microsoft/comments/1ihfwvj/interview_with_og_microsoft_devdiv_lead_yuval/", "upvotes": 2, "comments_count": 0, "sentiment": "neutral", "engagement_score": 2.0, "source_subreddit": "microsoft", "hashtags": null, "ticker": "MSFT"}, {"platform": "reddit", "post_id": "reddit_1ihgd9d", "title": "Preferred Work Location for New Grad Role", "content": "I’m a recent graduate starting a fully remote role at Microsoft. I'm considering relocating from the East Coast (DC) to **Seattle** or **Chicago** to boost my early career development.\n\nI loved my internship in Redmond. I was drawn to Seattle’s vibrant city energy, excellent public transit, and stunning nature. The PNW spring/summer climate fits my preference for weather, and being near Microsoft HQ could enhance my learning and networking opportunities.\n\nOn the other hand, Chicago also offers a dynamic tech scene and a large Microsoft office. As a sports fan, I appreciate that both cities are major sports hubs.\n\nI’d love to hear from anyone with experience as a new grad remote worker in Seattle, Chicago, or similar cities. How have your experiences been with learning opportunities, networking, and work-life balance? How did living in your city influence your ability to make friends and build a professional network? Thanks for your insights!", "author": "NickoHand", "created_time": "2025-02-04T12:19:15", "url": "https://reddit.com/r/microsoft/comments/1ihgd9d/preferred_work_location_for_new_grad_role/", "upvotes": 0, "comments_count": 4, "sentiment": "neutral", "engagement_score": 8.0, "source_subreddit": "microsoft", "hashtags": null, "ticker": "MSFT"}, {"platform": "reddit", "post_id": "reddit_1ihizm9", "title": "Microsoft Data Engineer Interview", "content": "I had my first interview round on January 28th. The recruiter asked me to be available for the entire day as they planned to conduct all interviews on the same day. \nHowever, it's been almost a week with no updates, despite my follow-ups.\n\nThe interview went well—I was able to answer 90% of the questions confidently.\n\n", "author": "Business_Art173", "created_time": "2025-02-04T14:33:43", "url": "https://reddit.com/r/microsoft/comments/1ihizm9/microsoft_data_engineer_interview/", "upvotes": 0, "comments_count": 8, "sentiment": "neutral", "engagement_score": 16.0, "source_subreddit": "microsoft", "hashtags": null, "ticker": "MSFT"}]