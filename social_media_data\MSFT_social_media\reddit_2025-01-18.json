[{"platform": "reddit", "post_id": "reddit_1i40czw", "title": "Microsoft Relocation", "content": "Hi there, \n\nDo Microsoft offer Relocation fees/bonus when you are hired and need to relocate to another town ?\nOne of the HR screeening questions was if I would be able to relocate on my own. I said YES but Im wondering if it’s common for Microsoft new hires.\n\nThank you", "author": "Ok_Present_8445", "created_time": "2025-01-18T05:12:37", "url": "https://reddit.com/r/microsoft/comments/1i40czw/microsoft_relocation/", "upvotes": 35, "comments_count": 18, "sentiment": "neutral", "engagement_score": 71.0, "source_subreddit": "microsoft", "hashtags": null, "ticker": "MSFT"}, {"platform": "reddit", "post_id": "reddit_1i48bp2", "title": "What to do with vested RSUs?", "content": "Are you holding onto those individual stocks? Or are you selling and diversifying in some ETF like VOO, VTI QQQ. \n\nI feel like if you were to invest in ETFs while still holding onto $MSFT or GOOG AMZN etc it would be redundant. Thoughts on how others have carried this situation out? I’m still holding onto my vested RSUs and thinking if I should diversify into my VOO portfolio? ", "author": "Ok-Intention-384", "created_time": "2025-01-18T14:11:15", "url": "https://reddit.com/r/microsoft/comments/1i48bp2/what_to_do_with_vested_rsus/", "upvotes": 48, "comments_count": 41, "sentiment": "bearish", "engagement_score": 130.0, "source_subreddit": "microsoft", "hashtags": null, "ticker": "MSFT"}, {"platform": "reddit", "post_id": "reddit_1i4eraf", "title": "Microsoft AutoGen v0.4: A turning point toward more intelligent AI agents for enterprise developers", "content": "", "author": "Ethan<PERSON>ill<PERSON>s_TG", "created_time": "2025-01-18T19:06:11", "url": "https://reddit.com/r/microsoft/comments/1i4eraf/microsoft_autogen_v04_a_turning_point_toward_more/", "upvotes": 3, "comments_count": 0, "sentiment": "neutral", "engagement_score": 3.0, "source_subreddit": "microsoft", "hashtags": null, "ticker": "MSFT"}, {"platform": "reddit", "post_id": "reddit_1i4iyq1", "title": "Your sign-in experience is changing", "content": "\"The web browser sign-in experience is changing when you sign in to any product or service using your Microsoft account. Starting in February 2025, ***you will stay signed in automatically*** unless you sign out or use private browsing.\" [Avoid staying signed in on a public computer](https://support.microsoft.com/en-us/account-billing/avoid-staying-signed-in-on-a-public-computer-d3f1448b-64b9-4b35-89d0-ce56715c6756)\n\nAm I the only one that thinks this change is moronic and will probably end badly for quite a few people?  ", "author": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "created_time": "2025-01-18T22:17:36", "url": "https://reddit.com/r/microsoft/comments/1i4iyq1/your_signin_experience_is_changing/", "upvotes": 20, "comments_count": 25, "sentiment": "neutral", "engagement_score": 70.0, "source_subreddit": "microsoft", "hashtags": null, "ticker": "MSFT"}, {"platform": "reddit", "post_id": "reddit_1i4j7h7", "title": "Microsoft 365 Business code stack", "content": "Does anyone know how long M365 Business Standard product keys can be stacked for?\n\n(I'm aware you can stack up to 5 years of M365 personal subscriptions using product keys, but do not want to assume the same for business as it's a different platform)", "author": "StormB2", "created_time": "2025-01-18T22:29:04", "url": "https://reddit.com/r/microsoft/comments/1i4j7h7/microsoft_365_business_code_stack/", "upvotes": 1, "comments_count": 0, "sentiment": "bullish", "engagement_score": 1.0, "source_subreddit": "microsoft", "hashtags": null, "ticker": "MSFT"}]