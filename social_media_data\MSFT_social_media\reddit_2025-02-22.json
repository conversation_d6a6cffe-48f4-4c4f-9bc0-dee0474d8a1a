[{"platform": "reddit", "post_id": "reddit_1ivrq0f", "title": "Copilot Pages, OneNote, Loop…where should I store my notes and tasks?", "content": "Microsoft always seems to release products which competes with already existing services. They are not overlapping 100% but they do it enough to confuse users.\n\nJust to give you some examples, we have Planner and ToDo, Loop and OneNote… and now Copilot Pages.\nThere might be others also but let’s focus on these.\n\nI started using Copilot more frequently and really like how integrated it is with Teams, Outlook and Sharepoint. Teams transcriptions with AI generated meeting notes is awesome.\n\nHowever, I am trying to find a good place to store all notes and tasks but I am so confused.\n\nWhat is the use cases for Copilot Pages vs Loop?\nIs Loop the future for storing notes or is OneNote still the best?\nAll the follow up tasks created, should I store them in ToDo or Planner?\n\nMy todos created in ToDo app seems to show up in Planner anyway, should I just use that one instead?\nAnd <PERSON> is the so called ”Notion killer” (or was supposed to be), is that the future for notes or should I use OneNote?\n\nI feel lost, how do you handle your notes and tasks in a M365 environment? \n", "author": "EN-D3R", "created_time": "2025-02-22T20:05:49", "url": "https://reddit.com/r/microsoft/comments/1ivrq0f/copilot_pages_onenote_loopwhere_should_i_store_my/", "upvotes": 70, "comments_count": 19, "sentiment": "neutral", "engagement_score": 108.0, "source_subreddit": "microsoft", "hashtags": null, "ticker": "MSFT"}, {"platform": "reddit", "post_id": "reddit_1ivswn9", "title": "My email address is dead to Microsoft due to failed Authenticator migration", "content": "I had been a reasonably happy user of Microsoft services for many years. I had Microsoft 365 and Visual Studio subscriptions. Some time ago something convinced me to add two-factor authentication to my login and that meant installing Microsoft Authenticator on my Google Pixel 6 phone. A few months ago, I replaced my phone with a brand new Google Pixel 9. As far as I know, I installed Authenticator on my new phone and everything was fine. Then I wiped my old phone and sent it in for recycling. \n\nA little bit later I found that Authenticator no longer did any authenticating. When I tried logging in via my PC, it said to watch for Authenticator to display a code but the code never came. I tried resetting my password and probably a few other things but I always ended up in a doom loop where it asked for a code that I could not supply.\n\nAfter searching online for help, I finally ended up in Microsoft support chat. I spent a few hours chatting with a representative but eventually it became clear that somehow my authentication credentials had not transferred properly from my old phone to my new phone. I was so naive to think that this just meant that I had to authenticate myself the hard way, by giving answers to secret questions, or sending in my picture id, or perhaps even showing up in person at some facility. Imagine my dismay when I was told that there is no backup authentication process. It is the end of the line for my email address at Microsoft. Perhaps if I faked my own death and got a court order to release the account to my wife, then they might help me. Other than that, I am screwed.\n\nI lost access to all my old calendar, contact, and email data. I had to get Microsoft to cancel all autopayments for my subscriptions. What a total lack of support! It seems to me there ought to be a law requiring a backup authentication method for all login procedures.", "author": "PaulTopping", "created_time": "2025-02-22T20:58:35", "url": "https://reddit.com/r/microsoft/comments/1ivswn9/my_email_address_is_dead_to_microsoft_due_to/", "upvotes": 0, "comments_count": 28, "sentiment": "bullish", "engagement_score": 56.0, "source_subreddit": "microsoft", "hashtags": null, "ticker": "MSFT"}, {"platform": "reddit", "post_id": "reddit_1ivu3dj", "title": "Microsoft SE initial Phone call", "content": "Microsoft SE initial Phone call with recruiter what kind of questions should I expect?", "author": "InevitableEye3955", "created_time": "2025-02-22T21:51:36", "url": "https://reddit.com/r/microsoft/comments/1ivu3dj/microsoft_se_initial_phone_call/", "upvotes": 0, "comments_count": 0, "sentiment": "neutral", "engagement_score": 0.0, "source_subreddit": "microsoft", "hashtags": null, "ticker": "MSFT"}]