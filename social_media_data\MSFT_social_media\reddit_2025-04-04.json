[{"platform": "reddit", "post_id": "reddit_1jr5d2i", "title": "19 year old looking to start portfolio", "content": "I’m 19 years old and currently studying Finance in university so I haven’t a lot of interest in the stock market. However, it’s very confusing to decide what moves to make while starting out. I’m looking to build a diverse portfolio over the years but not sure where to start out.\n\nI plan to invest 1,000 to the S & P 500 and another 1,000 in Microsoft stock ( I believe they wouldn’t be affected too much by recent tariffs) \n\nIs this a solid plan? Do I need a new or different strategy? I’ll be super grateful for any feedback or advice.", "author": "Accomplished-Tax7990", "created_time": "2025-04-04T06:30:00", "url": "https://reddit.com/r/investing_discussion/comments/1jr5d2i/19_year_old_looking_to_start_portfolio/", "upvotes": 3, "comments_count": 2, "sentiment": "neutral", "engagement_score": 7.0, "source_subreddit": "investing_discussion", "hashtags": null, "ticker": "MSFT"}, {"platform": "reddit", "post_id": "reddit_1jra0nu", "title": "Xbox Celebrates Microsoft's 50th Anniversary With Free Digital Content for Players", "content": "", "author": "elvenharps", "created_time": "2025-04-04T11:54:37", "url": "https://reddit.com/r/microsoft/comments/1jra0nu/xbox_celebrates_microsofts_50th_anniversary_with/", "upvotes": 28, "comments_count": 0, "sentiment": "neutral", "engagement_score": 28.0, "source_subreddit": "microsoft", "hashtags": null, "ticker": "MSFT"}, {"platform": "reddit", "post_id": "reddit_1jrecbz", "title": "50 years ago today, <PERSON> and I started this little thing called Microsoft", "content": "I'm thrilled to be in Redmond today with <PERSON>, <PERSON><PERSON><PERSON>, and so many others who helped make Microsoft what it is—as we celebrate an incredible milestone. Looking back on the company’s 50-year journey always fills me with pride and gratitude. It’s amazing to think how far we’ve come since <PERSON> and I were hunched over the PDP-10 in Harvard’s computer lab, writing the code that would become our first product. That moment sparked a lifetime of innovation, and I can’t wait to see what the next 50 years will bring.", "author": "thisisbillgates", "created_time": "2025-04-04T15:14:57", "url": "https://reddit.com/r/microsoft/comments/1jrecbz/50_years_ago_today_paul_and_i_started_this_little/", "upvotes": 3263, "comments_count": 432, "sentiment": "neutral", "engagement_score": 4127.0, "source_subreddit": "microsoft", "hashtags": null, "ticker": "MSFT"}, {"platform": "reddit", "post_id": "reddit_1jrehe3", "title": "Need some advice on my portfolio", "content": "I had $6000 at peak invested in 6 stocks on Fidelity app. Amazon Microsoft Tesla SPGY SPY TSM and finally NVIDIA.\n\nHeres my problem. I have 1 share of every other stock but I have 26 shares of NVIDIA, I bought low at 120 expecting another peak to sell around 140 to then redistribute among my current stocks and even widen my margin. But now NVIDIA has plummeted to $93 a share and I’m down $500 if I sell now. \n\nDo I hold and wait to break even then redistribute or do I take my loss and wait for better days with a more diverse portfolio. Thanks", "author": "Boring_Most_5343", "created_time": "2025-04-04T15:20:42", "url": "https://reddit.com/r/investing_discussion/comments/1jrehe3/need_some_advice_on_my_portfolio/", "upvotes": 3, "comments_count": 1, "sentiment": "bearish", "engagement_score": 5.0, "source_subreddit": "investing_discussion", "hashtags": null, "ticker": "MSFT"}, {"platform": "reddit", "post_id": "reddit_1jriqjl", "title": "Microsoft employee disrupts 50th anniversary and calls AI boss ‘war profiteer’", "content": "", "author": "esporx", "created_time": "2025-04-04T18:18:20", "url": "https://reddit.com/r/microsoft/comments/1jriqjl/microsoft_employee_disrupts_50th_anniversary_and/", "upvotes": 3916, "comments_count": 179, "sentiment": "bullish", "engagement_score": 4274.0, "source_subreddit": "microsoft", "hashtags": null, "ticker": "MSFT"}]