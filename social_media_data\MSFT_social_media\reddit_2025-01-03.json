[{"platform": "reddit", "post_id": "reddit_1hsn2ff", "title": " What GUI framework does Microsoft's Phone Link app on iOS use? .NET MAUI or Native Frameworks?", "content": "I'm curious about the **Phone Link** app by Microsoft on iOS. I know that Microsoft uses various frameworks for their apps across platforms, but I can't seem to find clear information on the GUI framework used in the iOS version of Phone Link.\n\nDoes anyone know if Microsoft uses **.NET MAUI** for the iOS version of Phone Link, or do they stick to native frameworks like **UIKit** (or maybe even **SwiftUI**) for iOS development?\n\nIt would be interesting to know how they approach the UI development for such an app, especially considering the cross-platform nature of the app and the performance needs on iOS.\n\nThanks for any insights!", "author": "DazzlingPassion614", "created_time": "2025-01-03T13:59:58", "url": "https://reddit.com/r/microsoft/comments/1hsn2ff/what_gui_framework_does_microsofts_phone_link_app/", "upvotes": 27, "comments_count": 3, "sentiment": "neutral", "engagement_score": 33.0, "source_subreddit": "microsoft", "hashtags": null, "ticker": "MSFT"}, {"platform": "reddit", "post_id": "reddit_1hsn2it", "title": "Amazon's Seattle campus still quiet as 5-days-in-office deadline hits", "content": "", "author": "AmazonNewsBot", "created_time": "2025-01-03T14:00:05", "url": "https://reddit.com/r/amazon/comments/1hsn2it/amazons_seattle_campus_still_quiet_as/", "upvotes": 59, "comments_count": 28, "sentiment": "neutral", "engagement_score": 115.0, "source_subreddit": "amazon", "hashtags": null, "ticker": "MSFT"}, {"platform": "reddit", "post_id": "reddit_1ht0plv", "title": "Does RCS for Phone Link no longer work?", "content": "I have been using phone link recently on my Windows 11 PC.\n\nMy phone is a Samsung Galaxy S24 Ultra.\n\nI saw on [Microsoft's support](https://support.microsoft.com/en-us/topic/supported-devices-for-phone-link-experiences-cb044172-87aa-9e41-d446-c4ac83ce8807) that my phone is supported for RCS on phone link.\n\nThe main issue is it says Samsung Messages has to be default but Samsung uses google messages, Samsung messages does not seem to be available anymore.\n\nDoes anyone know if this just doesn't work anymore?", "author": "Captain<PERSON><PERSON><PERSON><PERSON><PERSON>", "created_time": "2025-01-03T23:45:00", "url": "https://reddit.com/r/microsoft/comments/1ht0plv/does_rcs_for_phone_link_no_longer_work/", "upvotes": 10, "comments_count": 9, "sentiment": "bullish", "engagement_score": 28.0, "source_subreddit": "microsoft", "hashtags": null, "ticker": "MSFT"}]