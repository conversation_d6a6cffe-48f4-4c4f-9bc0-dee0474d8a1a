[{"platform": "reddit", "post_id": "reddit_1jokp62", "title": "Thanks for the cost increase /s", "content": "A company that is in no way hurting unless it is simply the excess of the senior leadership or the continually failing gaming division.\n\nLayoffs are happening and the solution is to raise the license price on a program that SHOULDN'T be a subscription based program.\n\nLooking for alternatives now and will happily be cancelling if I can find one. Absolute joke.\n\nEdit: Microsoft 365 is the price increase it's for AI. I was able to drop the price by choosing not to have the AI add on. Automatically adding me into it? Anti consumer and bullshit. Also this software license subscription is also bullshit. ", "author": "Fragrant_Wedding_606", "created_time": "2025-04-01T02:08:25", "url": "https://reddit.com/r/microsoft/comments/1jokp62/thanks_for_the_cost_increase_s/", "upvotes": 0, "comments_count": 6, "sentiment": "bullish", "engagement_score": 12.0, "source_subreddit": "microsoft", "hashtags": null, "ticker": "MSFT"}, {"platform": "reddit", "post_id": "reddit_1jon6s5", "title": "More Budget Cuts & Layoffs for FTEs?", "content": "I started with Microsoft back in October. Now I’m hearing more rumors recently of extreme budget cuts & hiring freezes for the cloud & devices BUs. Anyone have any insight on what’s going on now? Also, is this normal for Microsoft?", "author": "moneynorms", "created_time": "2025-04-01T04:25:49", "url": "https://reddit.com/r/microsoft/comments/1jon6s5/more_budget_cuts_layoffs_for_ftes/", "upvotes": 32, "comments_count": 21, "sentiment": "neutral", "engagement_score": 74.0, "source_subreddit": "microsoft", "hashtags": null, "ticker": "MSFT"}, {"platform": "reddit", "post_id": "reddit_1joq4a4", "title": "Microsoft 365 worth it?", "content": "I'm torn between purchasing Microsoft Office 2021 or subscribing to Microsoft 365. (for personal use)\n\n\n\nI would obviously prefer a one off payment (I think most people would) and don’t particularly mind missing out on the latest features (so long as I have the tools required to complete the task then I can't really complain). And I guess I wouldn't \\*need\\* to work online (or offline, for that matter) and I'm not sure I'd \\*\\*need\\*\\* advanced cloud access or anything.\n\n  \n\\*\\*BUT\\*\\* the features of Microsoft 365 \\*are\\* appealing and would probably be beneficial(??). \n\n  \nFor those of you with more understanding of this and bigger brains - is 365 worth it?", "author": "AllYouNeedIsACupOTea", "created_time": "2025-04-01T07:47:51", "url": "https://reddit.com/r/microsoft/comments/1joq4a4/microsoft_365_worth_it/", "upvotes": 11, "comments_count": 26, "sentiment": "bullish", "engagement_score": 63.0, "source_subreddit": "microsoft", "hashtags": null, "ticker": "MSFT"}, {"platform": "reddit", "post_id": "reddit_1joqw06", "title": "Word + Windows 11 Preview Pane Error: \"Word could not create the work file\" - Persistent popup until Task Manager kill - Workaround inside", "content": "**TL;DR:**  \nOffice 2016 MSI + Windows 11 Preview Pane = Word popup hell  \nDisabled the Word preview handler via registry → No more popups, other previews still work.  \n\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\n\nHey all,\n\nJust wanted to share my experience and workaround for a super annoying issue that I recently struggled with, in case it helps someone else.\n\n**My situation:**\n\n* Windows 11 (64-bit)\n* Microsoft Office 2016 (MSI install, NOT Click-to-Run, NOT Office 365)\n* I use the Windows Explorer Preview Pane a lot to quickly glance at files\n* Suddenly, after some Windows or Office update (unsure when exactly), Word files started throwing the following error the moment I selected them in Explorer:\n\n>\n\n**The issue:**\n\n* The popup would come up immediately and in a loop\n* Clicking \"OK\" would bring it back instantly\n* Only way out was ending Word via Task Manager\n* The Preview Pane worked fine for other file types (images, PDFs, etc.)\n* Excel previews also stopped working, but without the popup\n\n**What I checked (and wasted hours on):**\n\n* `Cache` registry key was correct: `%USERPROFILE%\\AppData\\Local\\Microsoft\\Windows\\INetCache`\n* `Content.Word` and `Content.Outlook` folders existed and had proper permissions\n* I did `winword /r` (registration) — no effect\n* I tried fixing the Preview Handlers via registry — no effect\n* I even tried repairing Office — still broken\n\n**The cause (probably):** It seems that Office 2016 (MSI version) preview handlers are no longer playing nice with modern Windows builds (especially 22H2+), particularly on 64-bit Windows with 32-bit Office. The Preview Pane tries to use a COM-based preview handler that fails.\n\n**My workaround:**  \nSince I just needed the Preview Pane for images, PDFs, and other non-Office files, I simply **disabled the Word Preview Handler** via the registry:\n\n1. Opened `regedit`\n2. Went to: `HKEY_LOCAL_MACHINE\\SOFTWARE\\WOW6432Node\\Microsoft\\Windows\\CurrentVersion\\PreviewHandlers`\n3. Found the string: `{84F66100-FF7C-4fb4-B0C0-02CD7FB668FE} = Microsoft Word previewer`\n4. Renamed it to: `{84F66100-FF7C-4fb4-B0C0-02CD7FB668FE}_disabled`\n\nAfter that:\n\n* Word documents no longer triggered the Preview Pane\n* No more popups\n* Preview Pane works fine for all other file types\n\nNot ideal, but I can live without Word previews — much better than killing Word every time via Task Manager.\n\n**Just wanted to share it, because I went through a lot of so called 'fixes' that didn't do anything for me.**", "author": "Solid-Commission6850", "created_time": "2025-04-01T08:46:01", "url": "https://reddit.com/r/microsoft/comments/1joqw06/word_windows_11_preview_pane_error_word_could_not/", "upvotes": 3, "comments_count": 1, "sentiment": "bullish", "engagement_score": 5.0, "source_subreddit": "microsoft", "hashtags": null, "ticker": "MSFT"}, {"platform": "reddit", "post_id": "reddit_1jp1r85", "title": "Please stop with all the copilot renaming", "content": "Really sad to watch all the products forced to have copilot in the name.  Here is the latest and simply depressing collapse of common sense …..\n\nhttps://www.linkedin.com/posts/jessli2117_microsoft-dynamics365-dynamics365customerservice-activity-7311456682088226817-YF4J", "author": "Other_Sign_6088", "created_time": "2025-04-01T17:42:33", "url": "https://reddit.com/r/microsoft/comments/1jp1r85/please_stop_with_all_the_copilot_renaming/", "upvotes": 144, "comments_count": 54, "sentiment": "neutral", "engagement_score": 252.0, "source_subreddit": "microsoft", "hashtags": null, "ticker": "MSFT"}]