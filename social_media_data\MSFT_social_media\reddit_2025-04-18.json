[{"platform": "reddit", "post_id": "reddit_1k1xdgz", "title": "Microsoft Fall 2025 Internship Toronto", "content": "Did anyone hear back from Microsoft for the fall business operations or finance internships ? ", "author": "Mammoth-Nebula1731", "created_time": "2025-04-18T05:27:30", "url": "https://reddit.com/r/microsoft/comments/1k1xdgz/microsoft_fall_2025_internship_toronto/", "upvotes": 0, "comments_count": 1, "sentiment": "bearish", "engagement_score": 2.0, "source_subreddit": "microsoft", "hashtags": null, "ticker": "MSFT"}, {"platform": "reddit", "post_id": "reddit_1k2fgtt", "title": "Why do Microsoft certifications not tell you what you missed?", "content": "I've taken about a half dozen Azure and other certifications, and failed my first one last month. \n\n  \nI noticed during this process that when I did fail the exam, it tells you the sections that you did not sore well not, but does not give you an idea of what question you missed or answer you should have chosen. I realize that these are tightly guarded Fort Knox like secrets to prevent cheating, but it would be great if the exams could give you more detailed information about topics or things that you need to study to actually learn from your failure.\n\n  \nStudying practice exams and Codecademy courses all day is great, but if the material you study is only 80% related to the questions on the exam, then it would be nice to have other sources to study or learn from or at least know what you need to do to grow.", "author": "f00dl3", "created_time": "2025-04-18T20:58:10", "url": "https://reddit.com/r/microsoft/comments/1k2fgtt/why_do_microsoft_certifications_not_tell_you_what/", "upvotes": 110, "comments_count": 5, "sentiment": "neutral", "engagement_score": 120.0, "source_subreddit": "microsoft", "hashtags": null, "ticker": "MSFT"}, {"platform": "reddit", "post_id": "reddit_1k2i1xh", "title": "Microsoft faces growing unrest over role in Israel’s war on Gaza: ‘Close to a tipping point’ | Technology", "content": "", "author": "Low_Razzmatazz3190", "created_time": "2025-04-18T22:56:50", "url": "https://reddit.com/r/microsoft/comments/1k2i1xh/microsoft_faces_growing_unrest_over_role_in/", "upvotes": 0, "comments_count": 4, "sentiment": "neutral", "engagement_score": 8.0, "source_subreddit": "microsoft", "hashtags": null, "ticker": "MSFT"}]