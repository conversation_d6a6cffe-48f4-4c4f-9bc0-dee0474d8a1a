[{"platform": "reddit", "post_id": "reddit_1ht27up", "title": "Dumping Memory to Bypass BitLocker on Windows 11", "content": "", "author": "NoInitialRamdisk", "created_time": "2025-01-04T00:53:56", "url": "https://reddit.com/r/microsoft/comments/1ht27up/dumping_memory_to_bypass_bitlocker_on_windows_11/", "upvotes": 0, "comments_count": 19, "sentiment": "bearish", "engagement_score": 38.0, "source_subreddit": "microsoft", "hashtags": null, "ticker": "MSFT"}, {"platform": "reddit", "post_id": "reddit_1ht70uv", "title": "Old MacBook Air", "content": "I have a 13-year-old MacBook Air that won't update OS, it's the last intel chip version..  Using Big Sur 11.9.   I have a Microsoft 365 subscription.  Is there a version that I can install on this laptop?  My primary reason to keep using it is to use bibliography manager plug in.", "author": "Craigccrncen", "created_time": "2025-01-04T05:05:37", "url": "https://reddit.com/r/microsoft/comments/1ht70uv/old_macbook_air/", "upvotes": 1, "comments_count": 3, "sentiment": "neutral", "engagement_score": 7.0, "source_subreddit": "microsoft", "hashtags": null, "ticker": "MSFT"}, {"platform": "reddit", "post_id": "reddit_1ht7hib", "title": "Microsoft expects to spend $80 billion on AI-enabled data centers in fiscal 2025", "content": "", "author": "ControlCAD", "created_time": "2025-01-04T05:32:27", "url": "https://reddit.com/r/microsoft/comments/1ht7hib/microsoft_expects_to_spend_80_billion_on/", "upvotes": 173, "comments_count": 29, "sentiment": "neutral", "engagement_score": 231.0, "source_subreddit": "microsoft", "hashtags": null, "ticker": "MSFT"}, {"platform": "reddit", "post_id": "reddit_1hta93a", "title": "LIQ<PERSON>DTEXT WOES ON SURFACE PRO 11- SEEKING ALTERNATIVE", "content": "Hello everyone!\n\nI’ve been a loyal user of LiquidText for my legal work, as it’s been a game-changer for managing case notes and documents. About six months ago, I decided to upgrade to a Surface Pro 11, excited about its ARM chip and overall utility for my profession. Unfortunately, that excitement quickly turned to frustration when I discovered the LiquidText app doesn’t work properly on the ARM chip.\n\nSince then, I’ve been in *constant* communication with the LiquidText team, trying to get the issue resolved. Six months later, all I have to show for my patience are lagging performance, frequent app crashes, and a series of unhelpful responses from their support team.\n\nHere’s the kicker: instead of solving the problem, the LiquidText team has essentially told me to *buy a new laptop*. Below are excerpts from their responses for your amusement:\n\n\"Hi,   We had been working on it. I am afraid the issue is with the ARM chip, this chip has been having issues with LiquidText for some time. Would you try any other device that runs with X64? I apologize for all the inconvenience.    Best wishes,\" \"We are working with the Microsoft team, and it seems like this specific surface model is having the same issues with LiquidText, a few other users have reported the issue as well. Microsoft is a bit slow at resolving issues, so the progress might come a bit late. I advise you to use any other laptop if you have any to see if the app is running better without any lags and crashes. \"\n\nEssentially, their solution to their app's failure is for *me* to shell out more money for new hardware. That’s like a tailor telling you to change your body because the suit doesn’t fit. Can anyone recommend apps similar to LiquidText that actually work seamlessly on the Surface Pro 11 with an ARM chip? I’d love to hear your thoughts and experiences because it seems like I need to jump ship.\n\nThanks in advance for your suggestions. And LiquidText, if you're reading this: maybe take a break from blaming Microsoft and focus on fixing your app.", "author": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "created_time": "2025-01-04T08:41:54", "url": "https://reddit.com/r/microsoft/comments/1hta93a/liquidtext_woes_on_surface_pro_11_seeking/", "upvotes": 1, "comments_count": 3, "sentiment": "bearish", "engagement_score": 7.0, "source_subreddit": "microsoft", "hashtags": null, "ticker": "MSFT"}, {"platform": "reddit", "post_id": "reddit_1htau73", "title": "Microsoft has finally upgraded notepad by replicating all features of notepad++ ", "content": "Using notepad was one of the most painful features of Microsoft OS. It did not auto save nor you had option of multiple tabs. But finally with W11 they have made it on par with notepad++\n\nBut why did microsoft take so long to do this ?", "author": "Infinite-Fold-1360", "created_time": "2025-01-04T09:25:53", "url": "https://reddit.com/r/microsoft/comments/1htau73/microsoft_has_finally_upgraded_notepad_by/", "upvotes": 0, "comments_count": 11, "sentiment": "bullish", "engagement_score": 22.0, "source_subreddit": "microsoft", "hashtags": null, "ticker": "MSFT"}]