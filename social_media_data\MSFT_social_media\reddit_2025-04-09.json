[{"platform": "reddit", "post_id": "reddit_1jutsid", "title": "Can someone please explain the CARTEL arrangement between Microsoft and GoDaddy", "content": "I regularly setup MS365 tenants for clients. Everytime I setup or am asked to setup a tenant for client that has their domain names registered with GoDaddy - i run in to endless problems. Literally GoDaddy is the worst Registrar, DNS and Web Hosting provider on the planet. In fact some software developers (for Joomla and Wordpress) will not support their software if installed on GoDaddy\n\nFor Microsoft - if i want to setup a domain  lets say [acme.com](http://acme.com) \\- i want this domains DNS to be managed by Microsoft - if the domain DNS or registration is already with GoDaddy - Microsoft FORCE you to use GoDaddy DNS - they remove the option to allow DNS management at MS365.\n\nWHY??\n\nWhat is this Cartel Arrangement.\n\nIn Australia - this is illegal - its called Third Party Enforcement. Ie. You as the second party are forcing me to use a Third party - you are removing the choice.", "author": "NoCream2189", "created_time": "2025-04-09T01:10:20", "url": "https://reddit.com/r/microsoft/comments/1jutsid/can_someone_please_explain_the_cartel_arrangement/", "upvotes": 0, "comments_count": 20, "sentiment": "bullish", "engagement_score": 40.0, "source_subreddit": "microsoft", "hashtags": null, "ticker": "MSFT"}, {"platform": "reddit", "post_id": "reddit_1juuk49", "title": "Reached an Annoying Character Limit on Google Docs, is Microsoft Word Better?", "content": "Writing a pretty big novel as a first entry to a western series, and I reached my character limit today in Google Docs at only just below 400 pages (book is predicted to be around 800ish pages) Scaling them down to smaller novels isn't possible because of how the story goes, so under these specific circumstances, is it better to just invest into Microsoft Word for my books?", "author": "FatPenguin26", "created_time": "2025-04-09T01:49:56", "url": "https://reddit.com/r/microsoft/comments/1juuk49/reached_an_annoying_character_limit_on_google/", "upvotes": 0, "comments_count": 11, "sentiment": "neutral", "engagement_score": 22.0, "source_subreddit": "microsoft", "hashtags": null, "ticker": "MSFT"}, {"platform": "reddit", "post_id": "reddit_1jv9lcp", "title": "Microsoft 'not moving forward' with $1B Licking County data center plans right now", "content": "", "author": "ControlCAD", "created_time": "2025-04-09T16:10:35", "url": "https://reddit.com/r/microsoft/comments/1jv9lcp/microsoft_not_moving_forward_with_1b_licking/", "upvotes": 192, "comments_count": 20, "sentiment": "neutral", "engagement_score": 232.0, "source_subreddit": "microsoft", "hashtags": null, "ticker": "MSFT"}, {"platform": "reddit", "post_id": "reddit_1jvdbak", "title": "This is a new low", "content": "**GK:**  \nI'd like to share that you can take advantage of subscribing to Copilot Pro that offers the latest GPT models, higher usage limits, priority access, higher quality images, image editing and other premium features in the Copilot experience. Additionally, for Microsoft 365 Personal and Family subscribers, Microsoft Copilot Pro brings the Copilot experience to Word, Excel, PowerPoint, and Outlook for use across home and work. Here’s the link for your overview: [https://support.microsoft.com/en-us/copilot-pro](https://support.microsoft.com/en-us/copilot-pro).  \n*Gene <PERSON> - 10:33 PM*\n\n**User:**  \nyeah  \n*10:33 PM - Sent*\n\n**User:**  \ncome on man, that is a new low.  \n*10:35 PM - Sent*\n\n**GK:**  \nJust a quick recap, you contacted Microsoft support because you needed help with automatic Windows update. After investigation, we have found out that the problem is due to the default system of Windows update. We resolved it by Changing details in Registry Editor and setting the time for active hours.  \n*<PERSON> - 10:35 PM*\n\n**User:**  \nplease do not tell me you do this to everyone who support talks to  \n*10:36 PM - Sent*\n\n**User:**  \ngot the recap thanks for the help, i know that the previous message isn't you, but that makes impressions. thank you for you help  \n*10:36 PM - Sent*\n\nno comment needed. ", "author": "Accomplished-Bed2289", "created_time": "2025-04-09T18:41:04", "url": "https://reddit.com/r/microsoft/comments/1jvdbak/this_is_a_new_low/", "upvotes": 0, "comments_count": 0, "sentiment": "neutral", "engagement_score": 0.0, "source_subreddit": "microsoft", "hashtags": null, "ticker": "MSFT"}, {"platform": "reddit", "post_id": "reddit_1jvdcqs", "title": "MSFT cutting PMs", "content": "Recently accepted an entry pm (US) job within security (start the fall). Will I be affected? ", "author": "Oreoblacklab", "created_time": "2025-04-09T18:42:44", "url": "https://reddit.com/r/microsoft/comments/1jvdcqs/msft_cutting_pms/", "upvotes": 93, "comments_count": 43, "sentiment": "bearish", "engagement_score": 179.0, "source_subreddit": "microsoft", "hashtags": null, "ticker": "MSFT"}, {"platform": "reddit", "post_id": "reddit_1jvizaj", "title": "Microsoft Intern Status", "content": "If the status in action status is completed with a new position, the old position is transferred and in the inactive folder, does this mean an offer is coming or like it can either be rejection also. Thanks for the help!", "author": "NewAppointment2190", "created_time": "2025-04-09T22:42:11", "url": "https://reddit.com/r/microsoft/comments/1jvizaj/microsoft_intern_status/", "upvotes": 0, "comments_count": 3, "sentiment": "neutral", "engagement_score": 6.0, "source_subreddit": "microsoft", "hashtags": null, "ticker": "MSFT"}, {"platform": "reddit", "post_id": "reddit_1jvjz6v", "title": "Microsoft hiring", "content": "Is there a cap for how many people can be interviewed for one job?\nAlso, is it normal for hiring managers to still interview a pool of people if they have a good option already working as a vendor?", "author": "throwaway-h8r", "created_time": "2025-04-09T23:29:33", "url": "https://reddit.com/r/microsoft/comments/1jvjz6v/microsoft_hiring/", "upvotes": 0, "comments_count": 9, "sentiment": "neutral", "engagement_score": 18.0, "source_subreddit": "microsoft", "hashtags": null, "ticker": "MSFT"}]