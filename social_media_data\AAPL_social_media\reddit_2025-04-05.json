[{"platform": "reddit", "post_id": "reddit_1jsanay", "title": "I’m wrestling with an existential question right now! Why doesn’t Microsoft simply eliminate MacBook Pros from the market?", "content": "Hello, I am a graphic designer currently looking for a new MacBook Pro. My current one, purchased four years ago, is no longer meeting my work needs. MacBook Pros are well-optimized for common graphic design tools like Photoshop, InDesign, and Illustrator. \n\nNotice, I didn’t include 3D software in this assessment because MacBooks lack the GPU and CPU power required for decent performance in those programs.\n\nWhile searching for a new MacBook, I started wondering why Microsoft hasn’t created an alternative to the MacBook Pro—one that combines the beauty and quality of a MacBook with a lighter version of Windows (perhaps Linux-based, but with full Windows support) and stronger hardware capabilities, like NVIDIA support.\n\nThey could let users decide whether to upgrade components or keep the laptop as is.\n\nthink Microsoft is missing out on a niche market here. I’d pay a fair amount for a sleek, high-performing laptop that blends Mac-like design with Microsoft’s product capabilities.\"\n\nP.S: They probably aren't missing out but don't want to eliminate macbook pro from the market, due to a business strategy. ", "author": "alcacobar", "created_time": "2025-04-05T18:52:49", "url": "https://reddit.com/r/microsoft/comments/1jsanay/im_wrestling_with_an_existential_question_right/", "upvotes": 0, "comments_count": 2, "sentiment": "bullish", "engagement_score": 4.0, "source_subreddit": "microsoft", "hashtags": null, "ticker": "AAPL"}]