[{"platform": "reddit", "post_id": "reddit_1hzd42a", "title": "Question regarding 365 subscription", "content": "Hi\n\nI have a microsoft 365 subscription through best buy as i got it with my laptop.  The renewal is coming up and its 175.  Since i last renewed, im currently not working and cant afford that amount.  \n\nMy question is if i were able to find a permanent license at a cheaper amount would i lose data? Another idea i had was to get it from the microsoft store as they have an option for a monthly 365 subscription.  However the same question arises.  \n\nI would appreciate any info on this.  Thank you!", "author": "Serenitymcw", "created_time": "2025-01-12T02:56:19", "url": "https://reddit.com/r/microsoft/comments/1hzd42a/question_regarding_365_subscription/", "upvotes": 1, "comments_count": 10, "sentiment": "bullish", "engagement_score": 21.0, "source_subreddit": "microsoft", "hashtags": null, "ticker": "MSFT"}, {"platform": "reddit", "post_id": "reddit_1hzn79b", "title": "What are your favorite Microsoft Loop features and hidden game-changers?", "content": "Hey everyone,\n\nI've been diving into Microsoft Loop recently and I'm really intrigued by what it offers. I'm curious to know:\n\n* **Which features do you love the most?** Are there particular functionalities that make your workflow a breeze?\n* **Hidden or underrated features?** Have you discovered any game-changing tricks or lesser-known functionalities that have significantly improved your experience?\n* **How do you use Loop?** Whether it's for team collaboration, project management, or something entirely different, I'd love to hear about your use cases.\n* **What are <PERSON>'s limitations?** Are there any pain points or areas where you feel <PERSON> falls short?\n\nLooking forward to hearing your insights and learning some new tips!", "author": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "created_time": "2025-01-12T13:57:37", "url": "https://reddit.com/r/microsoft/comments/1hzn79b/what_are_your_favorite_microsoft_loop_features/", "upvotes": 1, "comments_count": 6, "sentiment": "bearish", "engagement_score": 13.0, "source_subreddit": "microsoft", "hashtags": null, "ticker": "MSFT"}, {"platform": "reddit", "post_id": "reddit_1hztnzo", "title": "Whiteboard alternatives", "content": "Good day, my dear windows users! I've been using whiteboard for the past 2 months and let me tell you, it's *poo*. Do you guys know any other whiteboards that are at least optimized better? For example, I have about 15 screenshots on one of my whiteboards with tons of writing and it freezes so much, despite the fact that I've an Intel core i9 14th gen along with 32gb ram! Also, the pen in zoom is so much smoother than whiteboard's and I really like that. So if you do know of any better alternatives that don't require you to share your data, log in, etc please do tell me the name of the app. Thank you for reading!", "author": "Yuga_Avner", "created_time": "2025-01-12T18:46:53", "url": "https://reddit.com/r/microsoft/comments/1hztnzo/whiteboard_alternatives/", "upvotes": 1, "comments_count": 2, "sentiment": "bullish", "engagement_score": 5.0, "source_subreddit": "microsoft", "hashtags": null, "ticker": "MSFT"}, {"platform": "reddit", "post_id": "reddit_1hzvras", "title": "Copilot app redirects me to the cloud version & says coming soon - I thought copilot was already working for enterprise?", "content": "Using the WIn11 copilot app I could use it before i tried signing in and after signing in it's asking if i want work or personal. I select work and it redirects me to the cloud [https://copilot.cloud.microsoft/](https://copilot.cloud.microsoft/) and says coming soon. Is Copilot only available for personal right now or am I missing something?", "author": "P_<PERSON>_woker", "created_time": "2025-01-12T20:14:33", "url": "https://reddit.com/r/microsoft/comments/1hzvras/copilot_app_redirects_me_to_the_cloud_version/", "upvotes": 1, "comments_count": 2, "sentiment": "neutral", "engagement_score": 5.0, "source_subreddit": "microsoft", "hashtags": null, "ticker": "MSFT"}, {"platform": "reddit", "post_id": "reddit_1hzyykw", "title": "Microsoft rules out layoffs in India amid global job cuts", "content": "", "author": "TeaAndGrumpets", "created_time": "2025-01-12T22:31:51", "url": "https://reddit.com/r/microsoft/comments/1hzyykw/microsoft_rules_out_layoffs_in_india_amid_global/", "upvotes": 427, "comments_count": 78, "sentiment": "neutral", "engagement_score": 583.0, "source_subreddit": "microsoft", "hashtags": null, "ticker": "MSFT"}]