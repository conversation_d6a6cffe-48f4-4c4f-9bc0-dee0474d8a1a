[{"platform": "reddit", "post_id": "reddit_1j0s0fo", "title": "How does Bonus and Annual Stock Award work?", "content": "NG SWE offer letter says this:\n\n\"You will also be eligible for an annual bonus, ranging from zero to a maximum of 20% of your bonus eligible salary during the rewards period based on your performance. If you are a new hire, your first eligibility for a bonus will be determined based on your start date and will be reviewed each year per Microsoft eligibility rules.\" \n\n\n\nIs this same as Target Bonus???\n\n\n\nI am also curious about this, idk what this means: \n\n\"Annual Stock Award. You are also eligible to be considered for future Stock Awards based on your start date.\" \n\n\n\nIs it like you get extra stocks if you perform well?", "author": "Supreme-Philosopher", "created_time": "2025-03-01T05:13:27", "url": "https://reddit.com/r/microsoft/comments/1j0s0fo/how_does_bonus_and_annual_stock_award_work/", "upvotes": 2, "comments_count": 17, "sentiment": "neutral", "engagement_score": 36.0, "source_subreddit": "microsoft", "hashtags": null, "ticker": "MSFT"}, {"platform": "reddit", "post_id": "reddit_1j0umaj", "title": "Digital sovereignty: Microsoft finalizes EU data border for cloud services", "content": "", "author": "donutloop", "created_time": "2025-03-01T08:05:45", "url": "https://reddit.com/r/microsoft/comments/1j0umaj/digital_sovereignty_microsoft_finalizes_eu_data/", "upvotes": 127, "comments_count": 33, "sentiment": "neutral", "engagement_score": 193.0, "source_subreddit": "microsoft", "hashtags": null, "ticker": "MSFT"}, {"platform": "reddit", "post_id": "reddit_1j0ylho", "title": "Microsoft Skype is Now Microsoft Teams.", "content": "What do you think of Skype after 22 years of existence?", "author": "deleted", "created_time": "2025-03-01T12:40:43", "url": "https://reddit.com/r/microsoft/comments/1j0ylho/microsoft_skype_is_now_microsoft_teams/", "upvotes": 15, "comments_count": 13, "sentiment": "neutral", "engagement_score": 41.0, "source_subreddit": "microsoft", "hashtags": null, "ticker": "MSFT"}, {"platform": "reddit", "post_id": "reddit_1j0z0rj", "title": "Microsoft Copilot also comes as a MacOS program", "content": "", "author": "donutloop", "created_time": "2025-03-01T13:04:53", "url": "https://reddit.com/r/microsoft/comments/1j0z0rj/microsoft_copilot_also_comes_as_a_macos_program/", "upvotes": 12, "comments_count": 1, "sentiment": "neutral", "engagement_score": 14.0, "source_subreddit": "microsoft", "hashtags": null, "ticker": "MSFT"}, {"platform": "reddit", "post_id": "reddit_1j0znzm", "title": "Difference between CBI and Annual Rewards", "content": "Hi, what's the difference between Core Priority Based Incentive (CBI) and Annual Rewards in Microsoft?\n\nCan someone please explain how CBI works?", "author": "Recent-Two-3708", "created_time": "2025-03-01T13:39:11", "url": "https://reddit.com/r/microsoft/comments/1j0znzm/difference_between_cbi_and_annual_rewards/", "upvotes": 0, "comments_count": 6, "sentiment": "neutral", "engagement_score": 12.0, "source_subreddit": "microsoft", "hashtags": null, "ticker": "MSFT"}, {"platform": "reddit", "post_id": "reddit_1j10bhq", "title": "With <PERSON><PERSON> retiring, I will miss the ability to answer incoming calls automatically. Is there any chance that this feature can be added to Microsoft Teams?", "content": "Hello everyone,\n\nAs a longtime user of Skype, one feature I've come to rely on is its ability to automatically answer incoming calls. This has been incredibly useful for both personal and family calls, ensuring no one misses out on important conversations, even if they're momentarily away from their device.\n\nWith Skype retiring, I'm looking to make the transition to Microsoft Teams. While I appreciate the many features Teams offers, I noticed that it currently lacks the automatic call-answering capability that I've grown to depend on.\n\nI believe adding this feature to Microsoft Teams would greatly enhance its functionality and user experience. Not only would it benefit former Skype users like myself, but it would also provide added convenience to the broader Teams community.\n\nI'm reaching out to the community here to gather support and feedback. Have any of you also found this feature to be valuable? Let's come together and ask Microsoft to consider integrating automatic call-answering into Teams.\n\nThanks for your support and insights!", "author": "Candid_Chef8378", "created_time": "2025-03-01T14:12:02", "url": "https://reddit.com/r/microsoft/comments/1j10bhq/with_skype_retiring_i_will_miss_the_ability_to/", "upvotes": 4, "comments_count": 8, "sentiment": "bullish", "engagement_score": 20.0, "source_subreddit": "microsoft", "hashtags": null, "ticker": "MSFT"}, {"platform": "reddit", "post_id": "reddit_1j10nkz", "title": "Microsoft leveling during interviews", "content": "I am preparing for a TPM interview loop at Microsoft. The position is “Senior” TPM, but, after doing some more research, I think that is a lower level than I am.\n\nI have 15 years of experience in TPM across several industries in tech. I was hired as e5 at Meta in 2022 (I no longer work there due to a layoff). I am at a “Staff” level at my current company, a large corporation, and likely up for a promotion within the next year.\n\nMy salary expectations also fall more within the “Principal” level at Microsoft.\n\nMy questions are, can I be upleveled during the interview process, even though the original job description was “Senior”? Can I have a conversation with the recruiter about this? Any other tips?\n", "author": "ApartmentStunning484", "created_time": "2025-03-01T14:28:48", "url": "https://reddit.com/r/microsoft/comments/1j10nkz/microsoft_leveling_during_interviews/", "upvotes": 8, "comments_count": 25, "sentiment": "neutral", "engagement_score": 58.0, "source_subreddit": "microsoft", "hashtags": null, "ticker": "MSFT"}, {"platform": "reddit", "post_id": "reddit_1j117hu", "title": "Why Skype Shouldn't Be Retired: A Beloved Communication Tool", "content": "Hey everyone,\n\nI wanted to start a discussion about the recent announcement regarding the end of life (EoL) of Skype. As someone who has relied on Skype for years, I believe retiring it is a significant loss, and I'd like to share my thoughts on why it shouldn't be retired.\n\n**1. A Beloved and Trusted Platform**\n\nSkype has been a trusted communication tool for millions of users worldwide. It has connected friends, families, and colleagues across the globe, providing high-quality video and audio calls, messaging, and more. The familiarity and reliability of Skype have made it a go-to platform for staying connected with loved ones and conducting business meetings.\n\n**2. Ease of Use and Accessibility**\n\nOne of Skype's greatest strengths is its user-friendly interface and accessibility. It works seamlessly across various devices and operating systems, making it easy for users to connect regardless of their technical expertise. Retiring Skype would force many users to transition to alternative platforms, which may not offer the same ease of use and accessibility.\n\n**3. Unique Features**\n\nSkype offers several unique features that set it apart from other communication tools. From the ability to make international calls at affordable rates to integrated chat and file sharing, Skype has provided a comprehensive communication solution that many users have come to rely on. These features are not always fully replicated in other platforms.\n\n**4. Impact on Users**\n\nThe decision to retire Skype will have a significant impact on users who have built their communication routines around it. Many users, especially those who are not tech-savvy or have older devices, may face difficulties transitioning to new platforms. This disruption could affect personal and professional relationships that have thrived on Skype.\n\n**5. A Sense of Nostalgia**\n\nFor many of us, Skype holds a special place in our hearts. It has been a part of our lives for years, helping us celebrate special moments, stay connected with distant family members, and conduct important business discussions. The thought of losing this beloved platform is deeply saddening.\n\nIn conclusion, retiring Skype is a decision that will negatively impact millions of users who have come to rely on its unique features and user-friendly interface. I urge Microsoft to reconsider this decision and explore ways to keep Skype alive and supported. Let's rally together and show our support for this beloved platform.\n\nWhat are your thoughts on the retirement of Skype? Do you agree that it should be kept alive? Let's discuss!\n\nBest,  \nBackground Jello.", "author": "Background-Jello-221", "created_time": "2025-03-01T14:55:05", "url": "https://reddit.com/r/microsoft/comments/1j117hu/why_skype_shouldnt_be_retired_a_beloved/", "upvotes": 0, "comments_count": 16, "sentiment": "bullish", "engagement_score": 32.0, "source_subreddit": "microsoft", "hashtags": null, "ticker": "MSFT"}, {"platform": "reddit", "post_id": "reddit_1j15ncn", "title": "Will you switch to Microsoft Teams when <PERSON><PERSON> is retired?", "content": "Hey r/Microsoft community,\n\nAs we all know, <PERSON>pe is set to be retired soon, and I'm curious to hear what everyone plans to do next.\n\nAre you planning to switch to Microsoft Teams, or do you have other messaging apps in mind? Personally, I believe Teams might not be the best replacement for Skype, and I'm concerned about losing some of the features and user-friendliness that Skype offered.\n\nLet's discuss:\n\n* What features do you think Teams lacks compared to Skype?\n* Are there any other apps you are considering as a replacement?\n* How do you feel about this change in general?\n\nI hope to gather a lot of opinions here so we can draw Microsoft's attention and maybe even get them to reconsider their decision.\n\nLooking forward to your thoughts!", "author": "Candid_Chef8378", "created_time": "2025-03-01T18:06:36", "url": "https://reddit.com/r/microsoft/comments/1j15ncn/will_you_switch_to_microsoft_teams_when_skype_is/", "upvotes": 3, "comments_count": 48, "sentiment": "neutral", "engagement_score": 99.0, "source_subreddit": "microsoft", "hashtags": null, "ticker": "MSFT"}]