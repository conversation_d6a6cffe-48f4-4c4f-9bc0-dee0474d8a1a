[{"platform": "reddit", "post_id": "reddit_1jpb19z", "title": "MTA-STS outage?", "content": "Just saw that all requests to Microsoft's MTA-STS configuration are failing – including to:\n\n[`https://mta-sts.microsoft.com/.well-known/mta-sts.txt`](https://mta-sts.microsoft.com/.well-known/mta-sts.txt)  \n[`https://mta-sts.outlook.com/.well-known/mta-sts.txt`](https://mta-sts.outlook.com/.well-known/mta-sts.txt)  \n[`https://mta-sts.hotmail.com/.well-known/mta-sts.txt`](https://mta-sts.hotmail.com/.well-known/mta-sts.txt)  \n[`https://mta-sts.live.com/.well-known/mta-sts.txt`](https://mta-sts.live.com/.well-known/mta-sts.txt)\n\nScreenshot: [https://imgur.com/a/yeQkSqk](https://imgur.com/a/yeQkSqk)\n\nIt's frustrating, because I fetch the file to my domain configuration, and so MTA-STS is failing on my domains, too. Impact is limited, fortunately – but it sucks. Any news what's going on with their servers at the moment?\n\nFor comparison, Google's server is working well: [http://mta-sts.google.com/.well-known/mta-sts.txt](http://mta-sts.google.com/.well-known/mta-sts.txt)\n\nAnyone?\n\n(It's an outage/bug report, not a support request!)", "author": "rohepey422", "created_time": "2025-04-02T00:07:43", "url": "https://reddit.com/r/microsoft/comments/1jpb19z/mtasts_outage/", "upvotes": 1, "comments_count": 4, "sentiment": "neutral", "engagement_score": 9.0, "source_subreddit": "microsoft", "hashtags": null, "ticker": "MSFT"}, {"platform": "reddit", "post_id": "reddit_1jpkm7v", "title": "Microsoft has given up advertising the Surface products even on their official channels.", "content": "<PERSON> has been around for 40 years. <PERSON> is only 13 years old. It's way too early to give up. They should take their time and build the brand.", "author": "Typical-Yogurt-1992", "created_time": "2025-04-02T09:38:44", "url": "https://reddit.com/r/microsoft/comments/1jpkm7v/microsoft_has_given_up_advertising_the_surface/", "upvotes": 0, "comments_count": 3, "sentiment": "neutral", "engagement_score": 6.0, "source_subreddit": "microsoft", "hashtags": null, "ticker": "MSFT"}, {"platform": "reddit", "post_id": "reddit_1jpqm4d", "title": "Unknown charge by Microsoft", "content": "I found a charge for \"Microsoft Trial Benefi\" on my credit card statement but don't remember signing up for any Microsoft products. I believe it's a legit charge and was wondering if anyone knew if this is a auto charge for an Office product? I use windows 11 on my home PC but never use any of the office products.", "author": "Joker-<PERSON><PERSON><PERSON>", "created_time": "2025-04-02T14:55:55", "url": "https://reddit.com/r/microsoft/comments/1jpqm4d/unknown_charge_by_microsoft/", "upvotes": 0, "comments_count": 5, "sentiment": "neutral", "engagement_score": 10.0, "source_subreddit": "microsoft", "hashtags": null, "ticker": "MSFT"}]